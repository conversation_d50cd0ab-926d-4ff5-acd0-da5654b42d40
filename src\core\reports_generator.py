#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur de rapports pour GSCOM
Crée des rapports avec graphiques et analyses
"""

import logging
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from src.dal.database import db_manager

class ReportsGenerator:
    """Générateur de rapports avec graphiques"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def generate_sales_report(self, start_date, end_date):
        """Génère un rapport de ventes"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.commercial import Invoice, InvoiceStatus
                
                # Récupérer les factures payées dans la période
                invoices = session.query(Invoice).filter(
                    Invoice.date >= start_date,
                    Invoice.date <= end_date,
                    Invoice.status.in_([InvoiceStatus.PAID, InvoiceStatus.PARTIAL])
                ).all()
                
                # Calculer les métriques
                total_revenue = sum(float(inv.total or 0) for inv in invoices)
                total_invoices = len(invoices)
                avg_invoice = total_revenue / total_invoices if total_invoices > 0 else 0
                
                # Grouper par mois
                monthly_data = {}
                for invoice in invoices:
                    month_key = invoice.date.strftime("%Y-%m")
                    if month_key not in monthly_data:
                        monthly_data[month_key] = {"count": 0, "total": 0}
                    monthly_data[month_key]["count"] += 1
                    monthly_data[month_key]["total"] += float(invoice.total or 0)
                
                return {
                    "total_revenue": total_revenue,
                    "total_invoices": total_invoices,
                    "avg_invoice": avg_invoice,
                    "monthly_data": monthly_data,
                    "period": f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"
                }
                
        except Exception as e:
            self.logger.error(f"Erreur génération rapport ventes: {e}")
            return None
    
    def generate_clients_report(self):
        """Génère un rapport sur les clients"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.client import Client
                from src.dal.models.commercial import Invoice
                
                # Statistiques clients
                total_clients = session.query(Client).count()
                active_clients = session.query(Client).filter(Client.is_active == True).count()
                
                # Top clients par CA
                top_clients = session.query(
                    Client.name,
                    session.query(Invoice.total).filter(
                        Invoice.client_id == Client.id
                    ).scalar_subquery().label('total_ca')
                ).filter(Client.is_active == True).limit(10).all()
                
                return {
                    "total_clients": total_clients,
                    "active_clients": active_clients,
                    "inactive_clients": total_clients - active_clients,
                    "top_clients": [(name, float(ca or 0)) for name, ca in top_clients]
                }
                
        except Exception as e:
            self.logger.error(f"Erreur génération rapport clients: {e}")
            return None
    
    def generate_products_report(self):
        """Génère un rapport sur les produits"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.product import Product
                from src.dal.models.commercial import InvoiceLine
                
                # Statistiques produits
                total_products = session.query(Product).count()
                active_products = session.query(Product).filter(Product.is_active == True).count()
                
                # Produits en rupture de stock
                out_of_stock = session.query(Product).filter(
                    Product.track_stock == True,
                    Product.current_stock <= Product.min_stock
                ).count()
                
                # Produits les plus vendus
                top_products = session.query(
                    Product.name,
                    session.query(InvoiceLine.quantity).filter(
                        InvoiceLine.product_id == Product.id
                    ).scalar_subquery().label('total_qty')
                ).limit(10).all()
                
                return {
                    "total_products": total_products,
                    "active_products": active_products,
                    "out_of_stock": out_of_stock,
                    "top_products": [(name, float(qty or 0)) for name, qty in top_products]
                }
                
        except Exception as e:
            self.logger.error(f"Erreur génération rapport produits: {e}")
            return None


class ReportWidget(QWidget):
    """Widget d'affichage de rapport avec graphiques"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.reports_generator = ReportsGenerator()
        
        self.init_ui()
        
    def init_ui(self):
        """Initialise l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title = QLabel("📊 Rapports et Analyses")
        title.setObjectName("reportTitle")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4ff;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Boutons de génération
        sales_btn = QPushButton("📈 Rapport Ventes")
        sales_btn.clicked.connect(self.show_sales_report)
        header_layout.addWidget(sales_btn)
        
        clients_btn = QPushButton("👥 Rapport Clients")
        clients_btn.clicked.connect(self.show_clients_report)
        header_layout.addWidget(clients_btn)
        
        products_btn = QPushButton("📦 Rapport Produits")
        products_btn.clicked.connect(self.show_products_report)
        header_layout.addWidget(products_btn)
        
        layout.addLayout(header_layout)
        
        # Zone de contenu avec onglets
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("reportTabs")
        layout.addWidget(self.tab_widget)
        
        # Onglet par défaut
        self.show_welcome_tab()
        
        self.apply_styles()
        
    def show_welcome_tab(self):
        """Affiche l'onglet d'accueil"""
        welcome_widget = QWidget()
        welcome_layout = QVBoxLayout(welcome_widget)
        welcome_layout.setAlignment(Qt.AlignCenter)
        
        welcome_label = QLabel("📊 Bienvenue dans les Rapports GSCOM")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("font-size: 20px; color: #00d4ff; margin: 50px;")
        welcome_layout.addWidget(welcome_label)
        
        desc_label = QLabel("Sélectionnez un type de rapport pour commencer l'analyse")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 14px;")
        welcome_layout.addWidget(desc_label)
        
        self.tab_widget.addTab(welcome_widget, "Accueil")
        
    def show_sales_report(self):
        """Affiche le rapport de ventes"""
        # Période par défaut: 3 derniers mois
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        report_data = self.reports_generator.generate_sales_report(start_date, end_date)
        
        if report_data:
            widget = self.create_sales_report_widget(report_data)
            
            # Supprimer l'onglet s'il existe déjà
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "Ventes":
                    self.tab_widget.removeTab(i)
                    break
                    
            self.tab_widget.addTab(widget, "Ventes")
            self.tab_widget.setCurrentWidget(widget)
        else:
            QMessageBox.warning(self, "Erreur", "Impossible de générer le rapport de ventes")
            
    def create_sales_report_widget(self, data):
        """Crée le widget du rapport de ventes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Métriques principales
        metrics_frame = QFrame()
        metrics_frame.setObjectName("metricsFrame")
        metrics_layout = QHBoxLayout(metrics_frame)
        
        # Cartes de métriques
        metrics = [
            ("Chiffre d'affaires", f"{data['total_revenue']:,.0f} DA", "#27ae60"),
            ("Nombre de factures", str(data['total_invoices']), "#3498db"),
            ("Facture moyenne", f"{data['avg_invoice']:,.0f} DA", "#9b59b6")
        ]
        
        for title, value, color in metrics:
            card = self.create_metric_card(title, value, color)
            metrics_layout.addWidget(card)
            
        layout.addWidget(metrics_frame)
        
        # Graphique si matplotlib disponible
        if MATPLOTLIB_AVAILABLE and data['monthly_data']:
            chart_widget = self.create_sales_chart(data['monthly_data'])
            layout.addWidget(chart_widget)
        else:
            no_chart_label = QLabel("📊 Graphiques non disponibles (matplotlib requis)")
            no_chart_label.setAlignment(Qt.AlignCenter)
            no_chart_label.setStyleSheet("color: rgba(255, 255, 255, 0.5); font-style: italic;")
            layout.addWidget(no_chart_label)
            
        return widget
        
    def create_metric_card(self, title, value, color):
        """Crée une carte de métrique"""
        card = QFrame()
        card.setObjectName("metricCard")
        card.setFixedHeight(100)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"font-size: 24px; font-weight: bold; color: {color};")
        layout.addWidget(value_label)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 12px; color: rgba(255, 255, 255, 0.8);")
        layout.addWidget(title_label)
        
        return card
        
    def create_sales_chart(self, monthly_data):
        """Crée un graphique des ventes mensuelles"""
        if not MATPLOTLIB_AVAILABLE:
            return QLabel("Graphiques non disponibles")
            
        # Configurer matplotlib pour le thème sombre
        plt.style.use('dark_background')
        
        figure = Figure(figsize=(12, 6), facecolor='#2b2b2b')
        canvas = FigureCanvas(figure)
        
        ax = figure.add_subplot(111)
        ax.set_facecolor('#2b2b2b')
        
        # Préparer les données
        months = sorted(monthly_data.keys())
        revenues = [monthly_data[month]["total"] for month in months]
        
        # Créer le graphique
        bars = ax.bar(months, revenues, color='#00d4ff', alpha=0.8)
        
        # Personnaliser
        ax.set_title('Évolution du Chiffre d\'Affaires', color='white', fontsize=16, pad=20)
        ax.set_xlabel('Mois', color='white')
        ax.set_ylabel('Montant (DA)', color='white')
        ax.tick_params(colors='white')
        
        # Rotation des labels de mois
        plt.setp(ax.get_xticklabels(), rotation=45)
        
        # Ajouter les valeurs sur les barres
        for bar, revenue in zip(bars, revenues):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{revenue:,.0f}', ha='center', va='bottom', color='white', fontsize=10)
        
        figure.tight_layout()
        
        return canvas
        
    def show_clients_report(self):
        """Affiche le rapport clients"""
        QMessageBox.information(self, "Rapport Clients", "Fonctionnalité en cours de développement")
        
    def show_products_report(self):
        """Affiche le rapport produits"""
        QMessageBox.information(self, "Rapport Produits", "Fonctionnalité en cours de développement")
        
    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet("""
            #reportTabs {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            
            #metricsFrame {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            
            #metricCard {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            
            #metricCard:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(0, 212, 255, 0.3);
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.2),
                    stop:1 rgba(255, 0, 255, 0.2));
                border: 1px solid rgba(0, 212, 255, 0.5);
                border-radius: 8px;
                padding: 10px 15px;
                color: white;
                font-weight: 500;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(255, 0, 255, 0.3));
                border-color: #00d4ff;
            }
        """)
