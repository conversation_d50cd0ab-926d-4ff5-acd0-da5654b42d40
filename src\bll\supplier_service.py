#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service de gestion des fournisseurs
Logique métier pour les opérations sur les fournisseurs
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import or_, and_

from src.bll.base_service import BaseService
from src.dal.models.client import Supplier, SupplierType
from src.dal.database import db_manager


class SupplierService(BaseService):
    """Service pour la gestion des fournisseurs"""
    
    def __init__(self):
        super().__init__(Supplier)
        self.logger = logging.getLogger(__name__)

    def get_all_suppliers(self, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère tous les fournisseurs"""
        return self.get_all(session=session)

    def get_supplier_by_id(self, supplier_id: int, session: Optional[Session] = None) -> Optional[Supplier]:
        """Récupère un fournisseur par son ID"""
        return self.get_by_id(supplier_id, session=session)

    def create_supplier(self, data: Dict[str, Any]) -> bool:
        """Crée un nouveau fournisseur"""
        return self.create(data)

    def update_supplier(self, supplier_id: int, data: Dict[str, Any]) -> bool:
        """Met à jour un fournisseur"""
        return self.update(supplier_id, data)

    def delete_supplier(self, supplier_id: int) -> bool:
        """Supprime un fournisseur"""
        return self.delete(supplier_id)

    def search_suppliers(self, query: str, session: Optional[Session] = None) -> List[Supplier]:
        """Recherche des fournisseurs par nom, email ou téléphone"""
        try:
            use_session = session or db_manager.get_session()
            
            suppliers = use_session.query(Supplier).filter(
                or_(
                    Supplier.name.ilike(f"%{query}%"),
                    Supplier.email.ilike(f"%{query}%"),
                    Supplier.phone.ilike(f"%{query}%"),
                    Supplier.contact_person.ilike(f"%{query}%")
                )
            ).all()
            
            if not session:
                use_session.close()
                
            return suppliers
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la recherche de fournisseurs: {e}")
            if not session:
                use_session.close()
            return []

    def get_suppliers_by_type(self, supplier_type: SupplierType, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère les fournisseurs par type"""
        try:
            use_session = session or db_manager.get_session()
            
            suppliers = use_session.query(Supplier).filter(
                Supplier.supplier_type == supplier_type
            ).all()
            
            if not session:
                use_session.close()
                
            return suppliers
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération par type: {e}")
            if not session:
                use_session.close()
            return []

    def get_active_suppliers(self, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère les fournisseurs actifs"""
        try:
            use_session = session or db_manager.get_session()
            
            suppliers = use_session.query(Supplier).filter(
                Supplier.is_active == True
            ).all()
            
            if not session:
                use_session.close()
                
            return suppliers
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs actifs: {e}")
            if not session:
                use_session.close()
            return []

    def get_supplier_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """Calcule les statistiques des fournisseurs"""
        try:
            use_session = session or db_manager.get_session()
            
            total_suppliers = use_session.query(Supplier).count()
            active_suppliers = use_session.query(Supplier).filter(
                Supplier.is_active == True
            ).count()
            
            # Statistiques par type
            type_stats = {}
            for supplier_type in SupplierType:
                count = use_session.query(Supplier).filter(
                    Supplier.supplier_type == supplier_type
                ).count()
                type_stats[supplier_type.value] = count
            
            if not session:
                use_session.close()
                
            return {
                'total_suppliers': total_suppliers,
                'active_suppliers': active_suppliers,
                'inactive_suppliers': total_suppliers - active_suppliers,
                'type_distribution': type_stats
            }
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            if not session:
                use_session.close()
            return {}

    def validate_supplier_data(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """Valide les données d'un fournisseur"""
        # Vérifications obligatoires
        if not data.get('name', '').strip():
            return False, "Le nom du fournisseur est obligatoire"
        
        if not data.get('email', '').strip():
            return False, "L'email est obligatoire"
        
        # Validation de l'email
        email = data.get('email', '').strip()
        if '@' not in email or '.' not in email:
            return False, "Format d'email invalide"
        
        # Validation du téléphone si fourni
        phone = data.get('phone', '').strip()
        if phone and len(phone) < 8:
            return False, "Le numéro de téléphone doit contenir au moins 8 chiffres"
        
        return True, ""

    def create_supplier_with_validation(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """Crée un fournisseur avec validation"""
        # Valider les données
        is_valid, error_message = self.validate_supplier_data(data)
        if not is_valid:
            return False, error_message
        
        # Vérifier l'unicité de l'email
        try:
            with db_manager.get_session() as session:
                existing_supplier = session.query(Supplier).filter(
                    Supplier.email == data['email'].strip()
                ).first()
                
                if existing_supplier:
                    return False, "Un fournisseur avec cet email existe déjà"
        
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la vérification d'unicité: {e}")
            return False, "Erreur lors de la validation"
        
        # Créer le fournisseur
        success = self.create(data)
        if success:
            return True, "Fournisseur créé avec succès"
        else:
            return False, "Erreur lors de la création du fournisseur"

    def update_supplier_with_validation(self, supplier_id: int, data: Dict[str, Any]) -> tuple[bool, str]:
        """Met à jour un fournisseur avec validation"""
        # Valider les données
        is_valid, error_message = self.validate_supplier_data(data)
        if not is_valid:
            return False, error_message
        
        # Vérifier l'unicité de l'email (sauf pour le fournisseur actuel)
        try:
            with db_manager.get_session() as session:
                existing_supplier = session.query(Supplier).filter(
                    and_(
                        Supplier.email == data['email'].strip(),
                        Supplier.id != supplier_id
                    )
                ).first()
                
                if existing_supplier:
                    return False, "Un autre fournisseur avec cet email existe déjà"
        
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la vérification d'unicité: {e}")
            return False, "Erreur lors de la validation"
        
        # Mettre à jour le fournisseur
        success = self.update(supplier_id, data)
        if success:
            return True, "Fournisseur modifié avec succès"
        else:
            return False, "Erreur lors de la modification du fournisseur"

    def deactivate_supplier(self, supplier_id: int) -> bool:
        """Désactive un fournisseur au lieu de le supprimer"""
        try:
            with db_manager.get_session() as session:
                supplier = session.query(Supplier).filter(
                    Supplier.id == supplier_id
                ).first()
                
                if supplier:
                    supplier.is_active = False
                    session.commit()
                    self.logger.info(f"Fournisseur {supplier_id} désactivé")
                    return True
                else:
                    self.logger.warning(f"Fournisseur {supplier_id} non trouvé")
                    return False
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la désactivation: {e}")
            return False

    def reactivate_supplier(self, supplier_id: int) -> bool:
        """Réactive un fournisseur"""
        try:
            with db_manager.get_session() as session:
                supplier = session.query(Supplier).filter(
                    Supplier.id == supplier_id
                ).first()
                
                if supplier:
                    supplier.is_active = True
                    session.commit()
                    self.logger.info(f"Fournisseur {supplier_id} réactivé")
                    return True
                else:
                    self.logger.warning(f"Fournisseur {supplier_id} non trouvé")
                    return False
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la réactivation: {e}")
            return False

    def get_supplier_purchase_history(self, supplier_id: int, session: Optional[Session] = None) -> List[Dict[str, Any]]:
        """Récupère l'historique des achats d'un fournisseur"""
        # Cette méthode sera implémentée quand le module des achats sera créé
        try:
            use_session = session or db_manager.get_session()
            
            # Pour l'instant, retourner une liste vide
            # TODO: Implémenter quand les modèles d'achat seront disponibles
            
            if not session:
                use_session.close()
                
            return []
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération de l'historique: {e}")
            if not session:
                use_session.close()
            return []

    def export_suppliers_to_dict(self, session: Optional[Session] = None) -> List[Dict[str, Any]]:
        """Exporte les fournisseurs vers un format dictionnaire"""
        try:
            suppliers = self.get_all_suppliers(session=session)
            
            export_data = []
            for supplier in suppliers:
                supplier_dict = {
                    'id': supplier.id,
                    'name': supplier.name,
                    'email': supplier.email,
                    'phone': supplier.phone,
                    'address': supplier.address,
                    'contact_person': supplier.contact_person,
                    'supplier_type': supplier.supplier_type.value if supplier.supplier_type else None,
                    'is_active': supplier.is_active,
                    'created_at': supplier.created_at.isoformat() if supplier.created_at else None,
                    'updated_at': supplier.updated_at.isoformat() if supplier.updated_at else None
                }
                export_data.append(supplier_dict)
            
            return export_data
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'export: {e}")
            return []
