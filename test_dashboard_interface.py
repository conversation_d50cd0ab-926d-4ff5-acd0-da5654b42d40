#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'Interface Tableau de Bord Principal GSCOM
Reproduction exacte de la capture d'écran du dashboard
"""

import sys
import os
import logging
from datetime import datetime

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MockUser:
    """Utilisateur fictif pour les tests"""
    def __init__(self):
        self.first_name = "Administrateur"
        self.username = "admin"
        self.id = 1

class DashboardTestApp(QApplication):
    """Application de test pour le dashboard"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setApplicationName("GSCOM Dashboard Test")
        self.setApplicationVersion("1.0")
        
        # Utilisateur fictif
        self.current_user = MockUser()
        
        self.setup_test_environment()
        self.create_test_interface()
    
    def setup_test_environment(self):
        """Configure l'environnement de test"""
        print("🚀 Démarrage du test Interface Tableau de Bord GSCOM...")
        print("🚀 Configuration de l'environnement de test...")
        
        # Vérifier les dépendances
        try:
            from src.ui.styles.theme_manager import theme_manager
            print("✅ Theme Manager disponible")
            self.theme_manager = theme_manager
        except ImportError as e:
            print(f"⚠️ Theme Manager non disponible: {e}")
            self.create_mock_theme_manager()
        
        try:
            from src.ui.dashboard.main_dashboard_interface import MainDashboardInterface
            print("✅ Interface Dashboard disponible")
            self.interface_class = MainDashboardInterface
        except ImportError as e:
            print(f"❌ Interface Dashboard non disponible: {e}")
            return False
        
        return True
    
    def create_mock_theme_manager(self):
        """Crée un gestionnaire de thème fictif"""
        class MockThemeManager(QObject):
            theme_changed = pyqtSignal(str)
            
            def __init__(self):
                super().__init__()
                self.current_theme = "light"
            
            def set_theme(self, theme_name):
                print(f"🎨 Thème changé vers: {theme_name}")
                self.current_theme = theme_name
                self.theme_changed.emit(theme_name)
        
        # Créer le module fictif
        import types
        theme_module = types.ModuleType('theme_manager')
        theme_module.theme_manager = MockThemeManager()
        self.theme_manager = theme_module.theme_manager
        
        # L'ajouter aux modules système
        sys.modules['src.ui.styles.theme_manager'] = theme_module
    
    def create_test_interface(self):
        """Crée l'interface de test"""
        try:
            self.dashboard_interface = self.interface_class(self.current_user)
            self.dashboard_interface.show()
            
            print("✅ Interface Tableau de Bord créée avec succès")
            
            # Afficher les informations de test
            self.show_test_info()
            
        except Exception as e:
            print(f"❌ Erreur création interface: {e}")
            import traceback
            traceback.print_exc()
    
    def show_test_info(self):
        """Affiche les informations de test"""
        print("\n" + "="*70)
        print("🎨 INTERFACE TABLEAU DE BORD - TEST")
        print("="*70)
        print(f"📅 Démarré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
        print(f"👤 Utilisateur: {self.current_user.first_name}")
        print(f"🖥️ Résolution: {self.primaryScreen().size().width()}x{self.primaryScreen().size().height()}")
        print("\n📋 REPRODUCTION EXACTE DE LA CAPTURE D'ÉCRAN:")
        print("  ✅ Sidebar bleue (#1E40AF) avec logo GSCOM")
        print("  ✅ Navigation avec 8 modules (Dashboard actif)")
        print("  ✅ Dropdown 'Thème Système' en haut")
        print("  ✅ Section administrateur en bas")
        print("  ✅ Header blanc avec 'Bienvenue, Administrateur !'")
        print("  ✅ Bouton 'Paramètres' en haut à droite")
        print("  ✅ 5 Cartes KPI horizontales:")
        print("      • 125k DA Ventes (Bleu)")
        print("      • 28 Commandes (Vert)")
        print("      • 156 Clients (Orange)")
        print("      • 87% Revenus (Violet)")
        print("      • 324 Stock (Rouge)")
        print("  ✅ 2 Sections côte à côte:")
        print("      • Informations Système (gauche)")
        print("      • Activité Récente (droite)")
        print("  ✅ Actions Rapides en grille 4x2:")
        print("      • Nouveau Client, Nouvelle Facture, Gestion Clients, Rapports")
        print("      • Nouvelle Commande, Nouveau Produit, Statistiques, Paramètres")
        print("\n🎨 DESIGN SPECIFICATIONS:")
        print("  • Sidebar: #1E40AF (Bleu professionnel)")
        print("  • Background: #F8FAFC (Gris très clair)")
        print("  • Cards: Blanc avec bordures #E5E7EB")
        print("  • Typographie: Inter/Segoe UI")
        print("  • Bordures: 12px radius, ombres légères")
        print("  • Message bienvenue: #00BCD4 (Cyan)")
        print("  • Actions: Couleurs pastel variées")
        print("\n🧪 INSTRUCTIONS DE TEST:")
        print("  1. Vérifiez la correspondance avec la capture d'écran")
        print("  2. Testez la navigation entre modules")
        print("  3. Cliquez sur le bouton 'Paramètres'")
        print("  4. Testez les actions rapides (8 cartes)")
        print("  5. Vérifiez les sections Informations et Activité")
        print("  6. Testez le bouton 'Profil' en bas")
        print("  7. Vérifiez la responsivité")
        print("="*70)

def main():
    """Fonction principale"""
    print("🎨 Démarrage du test Interface Tableau de Bord GSCOM...")
    print("🎯 Objectif: Reproduire exactement la capture d'écran du dashboard")
    
    # Créer l'application
    app = DashboardTestApp(sys.argv)
    
    print("✅ Application de test créée")
    print("🎨 Interface moderne en cours de chargement...")
    
    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
