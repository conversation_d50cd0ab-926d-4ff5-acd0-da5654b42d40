#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Commerciale Moderne GSCOM
Hub central pour toutes les activités commerciales avec style cohérent au dashboard
"""

import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.styles.theme_manager import theme_manager
from src.bll.commercial_service import CommercialService

class ModernCommercialInterface(QMainWindow):
    """Interface commerciale moderne avec style dashboard"""
    
    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        self.commercial_service = CommercialService()
        
        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Module Commercial")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        self.setup_ui()
        self.apply_styles()
        self.load_data()
        
        # Connecter aux changements de thème
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header moderne
        self.create_header(main_layout)
        
        # Zone de contenu
        self.create_content_area(main_layout)
    
    def create_header(self, layout):
        """Crée l'en-tête moderne"""
        header = QFrame()
        header.setObjectName("commercialHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(20)
        
        # Titre et description
        title_section = QVBoxLayout()
        
        title = QLabel("💼 Module Commercial")
        title.setObjectName("pageTitle")
        title_section.addWidget(title)
        
        subtitle = QLabel("Gestion complète des activités commerciales")
        subtitle.setObjectName("pageSubtitle")
        title_section.addWidget(subtitle)
        
        header_layout.addLayout(title_section)
        header_layout.addStretch()
        
        # Actions rapides
        actions_layout = QHBoxLayout()
        
        # Bouton Nouveau Devis
        new_quote_btn = QPushButton("📝 Nouveau Devis")
        new_quote_btn.setObjectName("primaryButton")
        new_quote_btn.clicked.connect(self.new_quote)
        actions_layout.addWidget(new_quote_btn)
        
        # Bouton Nouvelle Commande
        new_order_btn = QPushButton("📋 Nouvelle Commande")
        new_order_btn.setObjectName("primaryButton")
        new_order_btn.clicked.connect(self.new_order)
        actions_layout.addWidget(new_order_btn)
        
        # Bouton Nouvelle Facture
        new_invoice_btn = QPushButton("🧾 Nouvelle Facture")
        new_invoice_btn.setObjectName("primaryButton")
        new_invoice_btn.clicked.connect(self.new_invoice)
        actions_layout.addWidget(new_invoice_btn)
        
        header_layout.addLayout(actions_layout)
        layout.addWidget(header)
    
    def create_content_area(self, layout):
        """Crée la zone de contenu principal"""
        content = QFrame()
        content.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(30, 20, 30, 30)
        content_layout.setSpacing(25)
        
        # KPI commerciaux
        self.create_commercial_kpis(content_layout)
        
        # Sections principales
        self.create_main_sections(content_layout)
        
        # Actions rapides commerciales
        self.create_commercial_actions(content_layout)
        
        layout.addWidget(content)
    
    def create_commercial_kpis(self, layout):
        """Crée les KPI commerciaux"""
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiSection")
        
        kpi_layout = QHBoxLayout(kpi_frame)
        kpi_layout.setContentsMargins(0, 0, 0, 0)
        kpi_layout.setSpacing(20)
        
        # Données KPI (seront chargées dynamiquement)
        kpi_data = [
            ("📝", "Devis", "12", "En cours", "#3B82F6"),
            ("📋", "Commandes", "8", "À traiter", "#10B981"),
            ("🚚", "Livraisons", "5", "En cours", "#F59E0B"),
            ("🧾", "Factures", "15", "En attente", "#8B5CF6"),
            ("💰", "CA Mensuel", "125k DA", "+18%", "#EF4444")
        ]
        
        for icon, title, value, subtitle, color in kpi_data:
            card = self.create_kpi_card(icon, title, value, subtitle, color)
            kpi_layout.addWidget(card)
        
        layout.addWidget(kpi_frame)
    
    def create_kpi_card(self, icon, title, value, subtitle, color):
        """Crée une carte KPI"""
        card = QFrame()
        card.setObjectName("kpiCard")
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(8)
        
        # Header avec icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("kpiIcon")
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        card_layout.addLayout(header_layout)
        
        # Valeur principale
        value_label = QLabel(value)
        value_label.setObjectName("kpiValue")
        card_layout.addWidget(value_label)
        
        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("kpiSubtitle")
        card_layout.addWidget(subtitle_label)
        
        return card
    
    def create_main_sections(self, layout):
        """Crée les sections principales"""
        sections_frame = QFrame()
        sections_layout = QHBoxLayout(sections_frame)
        sections_layout.setContentsMargins(0, 0, 0, 0)
        sections_layout.setSpacing(25)
        
        # Section Documents Récents
        recent_docs = self.create_recent_documents_section()
        sections_layout.addWidget(recent_docs)
        
        # Section Pipeline Commercial
        pipeline = self.create_pipeline_section()
        sections_layout.addWidget(pipeline)
        
        layout.addWidget(sections_frame)
    
    def create_recent_documents_section(self):
        """Crée la section documents récents"""
        section = QFrame()
        section.setObjectName("infoSection")
        
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(12)
        
        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("📄")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("Documents Récents")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        section_layout.addLayout(title_layout)
        
        # Liste des documents
        documents = [
            ("📝", "Devis DEV-2024-001", "Client ABC", "Il y a 2h"),
            ("📋", "Commande CMD-2024-015", "Société XYZ", "Il y a 4h"),
            ("🧾", "Facture FAC-2024-089", "Client DEF", "Il y a 1j"),
            ("🚚", "Livraison LIV-2024-023", "Entreprise GHI", "Il y a 2j"),
            ("💰", "Paiement PAY-2024-156", "Client JKL", "Il y a 3j")
        ]
        
        for icon, doc_name, client, time in documents:
            doc_layout = QHBoxLayout()
            
            doc_icon = QLabel(icon)
            doc_icon.setObjectName("docIcon")
            doc_layout.addWidget(doc_icon)
            
            doc_info = QVBoxLayout()
            doc_info.setSpacing(2)
            
            doc_title = QLabel(doc_name)
            doc_title.setObjectName("docTitle")
            doc_info.addWidget(doc_title)
            
            doc_client = QLabel(client)
            doc_client.setObjectName("docClient")
            doc_info.addWidget(doc_client)
            
            doc_layout.addLayout(doc_info)
            doc_layout.addStretch()
            
            doc_time = QLabel(time)
            doc_time.setObjectName("docTime")
            doc_layout.addWidget(doc_time)
            
            section_layout.addLayout(doc_layout)
        
        return section
    
    def create_pipeline_section(self):
        """Crée la section pipeline commercial"""
        section = QFrame()
        section.setObjectName("activitySection")
        
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(12)
        
        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("📈")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("Pipeline Commercial")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        section_layout.addLayout(title_layout)
        
        # Étapes du pipeline
        pipeline_steps = [
            ("🎯", "Prospects", "24", "#3B82F6"),
            ("📝", "Devis envoyés", "12", "#F59E0B"),
            ("🤝", "Négociation", "8", "#8B5CF6"),
            ("✅", "Commandes", "5", "#10B981"),
            ("💰", "Facturé", "3", "#EF4444")
        ]
        
        for icon, stage, count, color in pipeline_steps:
            stage_layout = QHBoxLayout()
            
            stage_icon = QLabel(icon)
            stage_icon.setObjectName("pipelineIcon")
            stage_icon.setStyleSheet(f"color: {color}; font-size: 16px;")
            stage_layout.addWidget(stage_icon)
            
            stage_name = QLabel(stage)
            stage_name.setObjectName("pipelineName")
            stage_layout.addWidget(stage_name)
            
            stage_layout.addStretch()
            
            stage_count = QLabel(count)
            stage_count.setObjectName("pipelineCount")
            stage_count.setStyleSheet(f"color: {color}; font-weight: bold;")
            stage_layout.addWidget(stage_count)
            
            section_layout.addLayout(stage_layout)
        
        return section
    
    def create_commercial_actions(self, layout):
        """Crée les actions rapides commerciales"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsSection")
        
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(15)
        
        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("⚡")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("Actions Commerciales")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        actions_layout.addLayout(title_layout)
        
        # Grille d'actions 4x2
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)
        
        # Actions commerciales
        actions = [
            ("📝", "Créer Devis", "new_quote", "#E8F4FD"),
            ("📋", "Nouvelle Commande", "new_order", "#E8F8F0"),
            ("🚚", "Planifier Livraison", "new_delivery", "#FFF2E8"),
            ("🧾", "Générer Facture", "new_invoice", "#F0E8FF"),
            ("👥", "Gérer Clients", "manage_clients", "#E8F4FD"),
            ("📊", "Rapports Ventes", "sales_reports", "#F8E8E8"),
            ("💰", "Suivi Paiements", "track_payments", "#FFF8E8"),
            ("📈", "Analyse Pipeline", "pipeline_analysis", "#F0E8FF")
        ]
        
        for i, (icon, title, action_id, bg_color) in enumerate(actions):
            row = i // 4
            col = i % 4
            
            action_card = self.create_action_card(icon, title, action_id, bg_color)
            grid_layout.addWidget(action_card, row, col)
        
        actions_layout.addLayout(grid_layout)
        layout.addWidget(actions_frame)
    
    def create_action_card(self, icon, title, action_id, bg_color):
        """Crée une carte d'action"""
        card = QPushButton()
        card.setObjectName("actionCard")
        card.setFixedHeight(80)
        card.clicked.connect(lambda: self.execute_action(action_id))
        card.setStyleSheet(f"background-color: {bg_color};")
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 10, 15, 10)
        card_layout.setSpacing(5)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("actionIcon")
        icon_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("actionTitle")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        return card

    # === MÉTHODES DE DONNÉES ===

    def load_data(self):
        """Charge les données commerciales"""
        try:
            # Charger les statistiques commerciales
            self.commercial_stats = self.commercial_service.get_commercial_stats()
            self.update_kpis()

        except Exception as e:
            self.logger.error(f"Erreur chargement données: {e}")

    def update_kpis(self):
        """Met à jour les KPI avec les vraies données"""
        if hasattr(self, 'commercial_stats'):
            # TODO: Mettre à jour les cartes KPI avec les vraies données
            pass

    # === MÉTHODES D'ACTIONS ===

    def new_quote(self):
        """Crée un nouveau devis"""
        self.logger.info("Création nouveau devis")
        # TODO: Ouvrir l'interface de création de devis

    def new_order(self):
        """Crée une nouvelle commande"""
        self.logger.info("Création nouvelle commande")
        # TODO: Ouvrir l'interface de création de commande

    def new_invoice(self):
        """Crée une nouvelle facture"""
        self.logger.info("Création nouvelle facture")
        # TODO: Ouvrir l'interface de création de facture

    def execute_action(self, action_id):
        """Exécute une action commerciale"""
        self.logger.info(f"Action commerciale: {action_id}")

        actions_map = {
            "new_quote": self.new_quote,
            "new_order": self.new_order,
            "new_delivery": self.new_delivery,
            "new_invoice": self.new_invoice,
            "manage_clients": self.manage_clients,
            "sales_reports": self.sales_reports,
            "track_payments": self.track_payments,
            "pipeline_analysis": self.pipeline_analysis
        }

        if action_id in actions_map:
            actions_map[action_id]()
        else:
            self.logger.warning(f"Action inconnue: {action_id}")

    def new_delivery(self):
        """Planifie une nouvelle livraison"""
        self.logger.info("Planification nouvelle livraison")

    def manage_clients(self):
        """Gère les clients"""
        self.logger.info("Gestion des clients")

    def sales_reports(self):
        """Affiche les rapports de ventes"""
        self.logger.info("Rapports de ventes")

    def track_payments(self):
        """Suit les paiements"""
        self.logger.info("Suivi des paiements")

    def pipeline_analysis(self):
        """Analyse le pipeline"""
        self.logger.info("Analyse du pipeline")

    # === MÉTHODES DE STYLE ===

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet(self.get_commercial_styles())

    def get_commercial_styles(self):
        """Retourne les styles CSS selon le thème"""
        try:
            current_theme = theme_manager.current_theme
        except:
            current_theme = "light"

        if current_theme == "dark":
            return self.get_dark_theme_styles()
        else:
            return self.get_light_theme_styles()

    def on_theme_changed(self, theme_name):
        """Réagit au changement de thème"""
        self.apply_styles()

    def get_light_theme_styles(self):
        """Styles pour thème clair - Cohérent avec le dashboard"""
        return """
        /* === CONFIGURATION GLOBALE === */
        QMainWindow {
            background: #F8FAFC;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === HEADER COMMERCIAL === */
        #commercialHeader {
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }

        #pageSubtitle {
            font-size: 14px;
            color: #6B7280;
            margin-top: 2px;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        /* === CONTENU PRINCIPAL === */
        #contentArea {
            background: #F8FAFC;
        }

        /* === CARTES KPI === */
        #kpiSection {
            background: transparent;
        }

        #kpiCard {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #kpiIcon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #6B7280;
        }

        #kpiValue {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
            margin: 5px 0;
        }

        #kpiSubtitle {
            font-size: 11px;
            color: #9CA3AF;
        }

        /* === SECTIONS === */
        #infoSection, #activitySection {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #docIcon, #pipelineIcon {
            font-size: 14px;
            color: #6B7280;
            margin-right: 8px;
        }

        #docTitle, #pipelineName {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
        }

        #docClient {
            font-size: 11px;
            color: #6B7280;
        }

        #docTime, #pipelineCount {
            font-size: 12px;
            color: #6B7280;
        }

        /* === ACTIONS RAPIDES === */
        #actionsSection {
            background: transparent;
        }

        #actionCard {
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #actionCard:hover {
            border-color: #3B82F6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        #actionIcon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        #actionTitle {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
        }
        """

    def get_dark_theme_styles(self):
        """Styles pour thème sombre"""
        return """
        /* === CONFIGURATION GLOBALE SOMBRE === */
        QMainWindow {
            background: #0F172A;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === HEADER SOMBRE === */
        #commercialHeader {
            background: #1E293B;
            border-bottom: 1px solid #334155;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #F8FAFC;
        }

        #pageSubtitle {
            font-size: 14px;
            color: #94A3B8;
            margin-top: 2px;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        /* === CONTENU SOMBRE === */
        #contentArea {
            background: #0F172A;
        }

        /* === CARTES KPI SOMBRES === */
        #kpiCard {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
        }

        #kpiValue {
            font-size: 24px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 5px 0;
        }

        #kpiSubtitle {
            font-size: 11px;
            color: #64748B;
        }

        /* === SECTIONS SOMBRES === */
        #infoSection, #activitySection {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #docIcon, #pipelineIcon {
            font-size: 14px;
            color: #94A3B8;
            margin-right: 8px;
        }

        #docTitle, #pipelineName {
            font-size: 13px;
            font-weight: 500;
            color: #F8FAFC;
        }

        #docClient {
            font-size: 11px;
            color: #94A3B8;
        }

        #docTime, #pipelineCount {
            font-size: 12px;
            color: #94A3B8;
        }

        /* === ACTIONS SOMBRES === */
        #actionCard {
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #actionCard:hover {
            border-color: #3B82F6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }

        #actionTitle {
            font-size: 12px;
            font-weight: 600;
            color: #F8FAFC;
        }
        """
