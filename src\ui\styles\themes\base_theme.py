#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thème de base pour GSCOM
Définit la structure commune à tous les thèmes
"""

from typing import Dict


class BaseTheme:
    """Classe de base pour tous les thèmes GSCOM"""
    
    def __init__(self):
        self.name = "base"
        self.colors = self.define_colors()
        self.styles = self.define_styles()
    
    def define_colors(self) -> Dict[str, str]:
        """Définit la palette de couleurs du thème"""
        return {
            # Couleurs principales
            'primary': '#00d4ff',
            'primary_dark': '#0099cc',
            'primary_light': '#33ddff',
            'secondary': '#6366f1',
            'secondary_dark': '#4f46e5',
            'secondary_light': '#8b5cf6',
            
            # Couleurs de fond
            'background': '#ffffff',
            'background_secondary': '#f8fafc',
            'surface': '#ffffff',
            'surface_variant': '#f1f5f9',
            
            # Couleurs de texte
            'text_primary': '#1e293b',
            'text_secondary': '#64748b',
            'text_tertiary': '#94a3b8',
            'text_inverse': '#ffffff',
            
            # Couleurs de bordure
            'border': '#e2e8f0',
            'border_light': '#f1f5f9',
            'border_dark': '#cbd5e1',
            
            # États
            'hover': 'rgba(0, 212, 255, 0.1)',
            'active': 'rgba(0, 212, 255, 0.2)',
            'focus': 'rgba(0, 212, 255, 0.3)',
            'disabled': '#94a3b8',
            
            # Couleurs sémantiques
            'success': '#10b981',
            'warning': '#f59e0b',
            'error': '#ef4444',
            'info': '#3b82f6',
            
            # Ombres
            'shadow_light': 'rgba(0, 0, 0, 0.05)',
            'shadow_medium': 'rgba(0, 0, 0, 0.1)',
            'shadow_dark': 'rgba(0, 0, 0, 0.2)',
        }
    
    def define_styles(self) -> str:
        """Définit les styles CSS de base"""
        return """
        /* Styles de base - à surcharger dans les thèmes spécifiques */
        """
    
    def get_colors(self) -> Dict[str, str]:
        """Retourne les couleurs du thème"""
        return self.colors
    
    def get_styles(self) -> str:
        """Retourne les styles CSS du thème"""
        return self.styles
    
    def is_dark(self) -> bool:
        """Indique si le thème est sombre"""
        return False
    
    def get_component_style(self, component: str) -> str:
        """Retourne le style d'un composant spécifique"""
        components = {
            'button': self.get_button_styles(),
            'input': self.get_input_styles(),
            'table': self.get_table_styles(),
            'card': self.get_card_styles(),
            'navigation': self.get_navigation_styles(),
        }
        return components.get(component, "")
    
    def get_button_styles(self) -> str:
        """Styles pour les boutons"""
        return f"""
        QPushButton {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 8px;
            padding: 10px 16px;
            color: {self.colors['text_primary']};
            font-weight: 500;
            font-size: 14px;
        }}
        
        QPushButton:hover {{
            background: {self.colors['hover']};
            border-color: {self.colors['primary']};
        }}
        
        QPushButton:pressed {{
            background: {self.colors['active']};
        }}
        """
    
    def get_input_styles(self) -> str:
        """Styles pour les champs de saisie"""
        return f"""
        QLineEdit, QTextEdit, QComboBox {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 8px;
            padding: 10px 12px;
            color: {self.colors['text_primary']};
            font-size: 14px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface']};
        }}
        """
    
    def get_table_styles(self) -> str:
        """Styles pour les tableaux"""
        return f"""
        QTableWidget {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 8px;
            gridline-color: {self.colors['border_light']};
        }}
        
        QTableWidget::item {{
            padding: 12px;
            border-bottom: 1px solid {self.colors['border_light']};
            color: {self.colors['text_primary']};
        }}
        
        QTableWidget::item:selected {{
            background: {self.colors['primary']};
            color: {self.colors['text_inverse']};
        }}
        """
    
    def get_card_styles(self) -> str:
        """Styles pour les cartes"""
        return f"""
        .card {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 12px;
            box-shadow: 0 2px 8px {self.colors['shadow_light']};
        }}
        """
    
    def get_navigation_styles(self) -> str:
        """Styles pour la navigation"""
        return f"""
        #sidebar {{
            background: {self.colors['surface']};
            border-right: 1px solid {self.colors['border']};
        }}
        
        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 12px;
            border-radius: 8px;
            margin: 2px 8px;
        }}
        
        #navButton:hover {{
            background: {self.colors['hover']};
        }}
        """
