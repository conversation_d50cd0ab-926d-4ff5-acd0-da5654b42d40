#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de notifications modernes pour GSCOM
Notifications toast, alertes et messages système
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class NotificationType:
    """Types de notifications"""
    SUCCESS = "success"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"

class NotificationWidget(QWidget):
    """Widget de notification toast moderne"""

    # Signal émis quand la notification est fermée
    closed = pyqtSignal()

    def __init__(self, message, notification_type=NotificationType.INFO, duration=3000, parent=None):
        super().__init__(parent)
        self.message = message
        self.notification_type = notification_type
        self.duration = duration

        self.setFixedHeight(80)
        self.setMinimumWidth(350)
        self.setMaximumWidth(500)

        # Configuration de la fenêtre
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)

        self.init_ui()
        self.setup_animations()

        # Timer pour fermeture automatique
        if duration > 0:
            QTimer.singleShot(duration, self.close_notification)

    def init_ui(self):
        """Initialise l'interface de la notification"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Icône
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setObjectName("notificationIcon")

        # Définir l'icône selon le type
        icons = {
            NotificationType.SUCCESS: "✅",
            NotificationType.INFO: "ℹ️",
            NotificationType.WARNING: "⚠️",
            NotificationType.ERROR: "❌"
        }
        icon_label.setText(icons.get(self.notification_type, "ℹ️"))
        layout.addWidget(icon_label)

        # Message
        message_label = QLabel(self.message)
        message_label.setObjectName("notificationMessage")
        message_label.setWordWrap(True)
        layout.addWidget(message_label, 1)

        # Bouton fermer
        close_button = QPushButton("✕")
        close_button.setFixedSize(24, 24)
        close_button.setObjectName("notificationCloseButton")
        close_button.clicked.connect(self.close_notification)
        layout.addWidget(close_button)

        self.apply_styles()

    def apply_styles(self):
        """Applique les styles selon le type de notification"""
        colors = {
            NotificationType.SUCCESS: {
                'bg': 'rgba(0, 255, 136, 0.9)',
                'border': '#00ff88',
                'text': '#ffffff'
            },
            NotificationType.INFO: {
                'bg': 'rgba(0, 212, 255, 0.9)',
                'border': '#00d4ff',
                'text': '#ffffff'
            },
            NotificationType.WARNING: {
                'bg': 'rgba(255, 170, 0, 0.9)',
                'border': '#ffaa00',
                'text': '#ffffff'
            },
            NotificationType.ERROR: {
                'bg': 'rgba(255, 107, 107, 0.9)',
                'border': '#ff6b6b',
                'text': '#ffffff'
            }
        }

        color_scheme = colors.get(self.notification_type, colors[NotificationType.INFO])

        self.setStyleSheet(f"""
            NotificationWidget {{
                background: {color_scheme['bg']};
                border: 2px solid {color_scheme['border']};
                border-radius: 12px;
                /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); */
            }}

            #notificationIcon {{
                font-size: 24px;
                color: {color_scheme['text']};
            }}

            #notificationMessage {{
                color: {color_scheme['text']};
                font-size: 14px;
                font-weight: 500;
                font-family: 'Segoe UI', sans-serif;
            }}

            #notificationCloseButton {{
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                color: {color_scheme['text']};
                font-weight: bold;
                font-size: 12px;
            }}

            #notificationCloseButton:hover {{
                background: rgba(255, 255, 255, 0.3);
            }}
        """)

    def setup_animations(self):
        """Configure les animations d'entrée et de sortie"""
        # Animation d'entrée (slide in from right)
        self.slide_in_animation = QPropertyAnimation(self, b"geometry")
        self.slide_in_animation.setDuration(300)
        self.slide_in_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de sortie (fade out)
        self.fade_out_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_out_animation.setDuration(200)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.finished.connect(self.hide)
        self.fade_out_animation.finished.connect(self.closed.emit)

    def show_notification(self, position=None):
        """Affiche la notification avec animation"""
        if position is None:
            # Position par défaut (coin supérieur droit)
            screen = QApplication.desktop().screenGeometry()
            x = screen.width() - self.width() - 20
            y = 20
            position = QPoint(x, y)

        # Position de départ (hors écran à droite)
        start_pos = QRect(position.x() + 300, position.y(), self.width(), self.height())
        end_pos = QRect(position.x(), position.y(), self.width(), self.height())

        self.setGeometry(start_pos)
        self.show()

        # Animation d'entrée
        self.slide_in_animation.setStartValue(start_pos)
        self.slide_in_animation.setEndValue(end_pos)
        self.slide_in_animation.start()

    def close_notification(self):
        """Ferme la notification avec animation"""
        self.fade_out_animation.start()

    def mousePressEvent(self, event):
        """Permet de fermer en cliquant sur la notification"""
        if event.button() == Qt.LeftButton:
            self.close_notification()


class NotificationManager(QObject):
    """Gestionnaire global des notifications"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            super().__init__()
            self.active_notifications = []
            self.notification_spacing = 90  # Espacement entre notifications
            self.initialized = True

    def show_notification(self, message, notification_type=NotificationType.INFO, duration=3000):
        """Affiche une nouvelle notification"""
        # Calculer la position
        screen = QApplication.desktop().screenGeometry()
        x = screen.width() - 370  # 350 (largeur) + 20 (marge)
        y = 20 + len(self.active_notifications) * self.notification_spacing

        # Créer la notification
        notification = NotificationWidget(message, notification_type, duration)
        notification.closed.connect(lambda: self.remove_notification(notification))

        # Ajouter à la liste
        self.active_notifications.append(notification)

        # Afficher
        notification.show_notification(QPoint(x, y))

        return notification

    def remove_notification(self, notification):
        """Supprime une notification et réorganise les autres"""
        if notification in self.active_notifications:
            self.active_notifications.remove(notification)
            notification.deleteLater()

            # Réorganiser les notifications restantes
            self.reorganize_notifications()

    def reorganize_notifications(self):
        """Réorganise les notifications après suppression"""
        screen = QApplication.desktop().screenGeometry()
        x = screen.width() - 370

        for i, notification in enumerate(self.active_notifications):
            new_y = 20 + i * self.notification_spacing
            current_geometry = notification.geometry()
            new_geometry = QRect(x, new_y, current_geometry.width(), current_geometry.height())

            # Animation de repositionnement
            animation = QPropertyAnimation(notification, b"geometry")
            animation.setDuration(200)
            animation.setStartValue(current_geometry)
            animation.setEndValue(new_geometry)
            animation.setEasingCurve(QEasingCurve.OutCubic)
            animation.start()

    def clear_all(self):
        """Ferme toutes les notifications"""
        for notification in self.active_notifications[:]:
            notification.close_notification()

    @staticmethod
    def success(message, duration=3000):
        """Affiche une notification de succès"""
        manager = NotificationManager()
        return manager.show_notification(message, NotificationType.SUCCESS, duration)

    @staticmethod
    def info(message, duration=3000):
        """Affiche une notification d'information"""
        manager = NotificationManager()
        return manager.show_notification(message, NotificationType.INFO, duration)

    @staticmethod
    def warning(message, duration=4000):
        """Affiche une notification d'avertissement"""
        manager = NotificationManager()
        return manager.show_notification(message, NotificationType.WARNING, duration)

    @staticmethod
    def error(message, duration=5000):
        """Affiche une notification d'erreur"""
        manager = NotificationManager()
        return manager.show_notification(message, NotificationType.ERROR, duration)


class AlertDialog(QDialog):
    """Dialogue d'alerte moderne"""

    def __init__(self, title, message, alert_type=NotificationType.INFO, parent=None):
        super().__init__(parent)
        self.alert_type = alert_type

        self.setWindowTitle(title)
        self.setFixedSize(400, 200)
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

        self.init_ui(title, message)
        self.apply_styles()

    def init_ui(self, title, message):
        """Initialise l'interface du dialogue"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête avec icône et titre
        header_layout = QHBoxLayout()

        # Icône
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setObjectName("alertIcon")

        icons = {
            NotificationType.SUCCESS: "✅",
            NotificationType.INFO: "ℹ️",
            NotificationType.WARNING: "⚠️",
            NotificationType.ERROR: "❌"
        }
        icon_label.setText(icons.get(self.alert_type, "ℹ️"))
        header_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("alertTitle")
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # Message
        message_label = QLabel(message)
        message_label.setObjectName("alertMessage")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        layout.addStretch()

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        ok_button = QPushButton("OK")
        ok_button.setObjectName("alertButton")
        ok_button.clicked.connect(self.accept)
        ok_button.setFixedSize(80, 35)
        buttons_layout.addWidget(ok_button)

        layout.addLayout(buttons_layout)

    def apply_styles(self):
        """Applique les styles selon le type d'alerte"""
        colors = {
            NotificationType.SUCCESS: '#00ff88',
            NotificationType.INFO: '#00d4ff',
            NotificationType.WARNING: '#ffaa00',
            NotificationType.ERROR: '#ff6b6b'
        }

        accent_color = colors.get(self.alert_type, colors[NotificationType.INFO])

        self.setStyleSheet(f"""
            AlertDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 2px solid {accent_color};
                border-radius: 15px;
            }}

            #alertIcon {{
                font-size: 36px;
                color: {accent_color};
            }}

            #alertTitle {{
                font-size: 18px;
                font-weight: bold;
                color: {accent_color};
                font-family: 'Segoe UI', sans-serif;
            }}

            #alertMessage {{
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                font-family: 'Segoe UI', sans-serif;
                line-height: 1.4;
            }}

            #alertButton {{
                background: {accent_color};
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', sans-serif;
            }}

            #alertButton:hover {{
                background: {accent_color}dd;
            }}
        """)

    @staticmethod
    def show_success(title, message, parent=None):
        """Affiche un dialogue de succès"""
        dialog = AlertDialog(title, message, NotificationType.SUCCESS, parent)
        return dialog.exec_()

    @staticmethod
    def show_info(title, message, parent=None):
        """Affiche un dialogue d'information"""
        dialog = AlertDialog(title, message, NotificationType.INFO, parent)
        return dialog.exec_()

    @staticmethod
    def show_warning(title, message, parent=None):
        """Affiche un dialogue d'avertissement"""
        dialog = AlertDialog(title, message, NotificationType.WARNING, parent)
        return dialog.exec_()

    @staticmethod
    def show_error(title, message, parent=None):
        """Affiche un dialogue d'erreur"""
        dialog = AlertDialog(title, message, NotificationType.ERROR, parent)
        return dialog.exec_()
