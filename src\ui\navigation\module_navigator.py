#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de Navigation Unifié GSCOM
Gère la navigation entre tous les modules avec style cohérent
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.dashboard import MainDashboardInterface
from src.ui.styles.theme_manager import theme_manager

class ModuleNavigator(QObject):
    """Gestionnaire de navigation entre modules"""
    
    module_changed = pyqtSignal(str)  # Signal émis lors du changement de module
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.current_user = None
        self.content_stack = None
        self.loaded_modules = {}
        
        # Configuration des modules disponibles
        self.modules_config = {
            "dashboard": {
                "title": "Tableau de bord",
                "icon": "📊",
                "class": "MainDashboardInterface",
                "module": "src.ui.dashboard",
                "description": "Vue d'ensemble et KPI"
            },
            "commercial": {
                "title": "Commercial",
                "icon": "💼",
                "class": "CommercialWidget",
                "module": "src.ui.modules.commercial",
                "description": "Gestion commerciale"
            },
            "orders": {
                "title": "Commandes",
                "icon": "📋",
                "class": "OrdersManagementInterface",
                "module": "src.ui.modules.orders_management_interface",
                "description": "Gestion des commandes"
            },
            "clients": {
                "title": "Clients",
                "icon": "👥",
                "class": "ClientsWidget",
                "module": "src.ui.modules.clients",
                "description": "Gestion des clients"
            },
            "products": {
                "title": "Produits",
                "icon": "📦",
                "class": "ProductsWidget",
                "module": "src.ui.modules.products",
                "description": "Catalogue produits"
            },
            "invoices": {
                "title": "Factures",
                "icon": "🧾",
                "class": "InvoicesWidget",
                "module": "src.ui.modules.invoices",
                "description": "Gestion des factures"
            },
            "quotes": {
                "title": "Devis",
                "icon": "📝",
                "class": "QuotesWidget",
                "module": "src.ui.modules.quotes",
                "description": "Gestion des devis"
            },
            "suppliers": {
                "title": "Fournisseurs",
                "icon": "🏭",
                "class": "SuppliersWidget",
                "module": "src.ui.modules.suppliers",
                "description": "Gestion des fournisseurs"
            },
            "stock": {
                "title": "Stock",
                "icon": "📋",
                "class": "StockWidget",
                "module": "src.ui.modules.stock",
                "description": "Gestion des stocks"
            },
            "reports": {
                "title": "Rapports",
                "icon": "📈",
                "class": "ReportsWidget",
                "module": "src.ui.modules.reports",
                "description": "Rapports et analyses"
            },
            "settings": {
                "title": "Paramètres",
                "icon": "⚙️",
                "class": "SettingsWidget",
                "module": "src.ui.modules.settings",
                "description": "Configuration"
            }
        }
    
    def set_content_stack(self, content_stack):
        """Définit le stack widget pour l'affichage des modules"""
        self.content_stack = content_stack
    
    def set_current_user(self, user):
        """Définit l'utilisateur actuel"""
        self.current_user = user
    
    def navigate_to_module(self, module_id):
        """Navigue vers un module spécifique"""
        if not self.content_stack:
            self.logger.error("Content stack non défini")
            return False
        
        if module_id not in self.modules_config:
            self.logger.error(f"Module inconnu: {module_id}")
            return False
        
        try:
            # Charger le module s'il n'est pas déjà en cache
            if module_id not in self.loaded_modules:
                module_widget = self.load_module(module_id)
                if module_widget:
                    self.loaded_modules[module_id] = module_widget
                    self.content_stack.addWidget(module_widget)
                else:
                    return False
            
            # Afficher le module
            module_widget = self.loaded_modules[module_id]
            self.content_stack.setCurrentWidget(module_widget)
            
            # Émettre le signal de changement
            self.module_changed.emit(module_id)
            
            self.logger.info(f"Navigation vers module: {module_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur navigation vers {module_id}: {e}")
            return False
    
    def load_module(self, module_id):
        """Charge un module spécifique"""
        config = self.modules_config[module_id]
        
        try:
            # Cas spécial pour le dashboard moderne
            if module_id == "dashboard":
                dashboard = MainDashboardInterface(self.current_user)
                
                # Créer un conteneur pour l'intégrer
                container = QWidget()
                layout = QVBoxLayout(container)
                layout.setContentsMargins(0, 0, 0, 0)
                layout.addWidget(dashboard.centralWidget())
                
                # Connecter la navigation du dashboard
                dashboard.switch_module = self.navigate_to_module
                
                return container
            
            # Cas spécial pour le module commandes moderne
            elif module_id == "orders":
                from src.ui.modules.orders_management_interface import OrdersManagementInterface
                orders_interface = OrdersManagementInterface(self.current_user)
                
                # Créer un conteneur
                container = QWidget()
                layout = QVBoxLayout(container)
                layout.setContentsMargins(0, 0, 0, 0)
                layout.addWidget(orders_interface.centralWidget())
                
                return container
            
            # Autres modules - chargement dynamique
            else:
                module_path = config["module"]
                class_name = config["class"]
                
                # Import dynamique
                module = __import__(module_path, fromlist=[class_name])
                module_class = getattr(module, class_name)
                
                # Créer l'instance
                if self.current_user:
                    module_widget = module_class(self.current_user)
                else:
                    module_widget = module_class()
                
                return module_widget
                
        except Exception as e:
            self.logger.error(f"Erreur chargement module {module_id}: {e}")
            
            # Créer un widget d'erreur
            error_widget = self.create_error_widget(module_id, str(e))
            return error_widget
    
    def create_error_widget(self, module_id, error_msg):
        """Crée un widget d'erreur pour les modules qui ne peuvent pas être chargés"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Icône d'erreur
        error_icon = QLabel("⚠️")
        error_icon.setAlignment(Qt.AlignCenter)
        error_icon.setStyleSheet("font-size: 48px; color: #F59E0B; margin: 20px;")
        layout.addWidget(error_icon)
        
        # Titre d'erreur
        error_title = QLabel(f"Module {self.modules_config[module_id]['title']} indisponible")
        error_title.setAlignment(Qt.AlignCenter)
        error_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #374151; margin: 10px;")
        layout.addWidget(error_title)
        
        # Message d'erreur
        error_message = QLabel(f"Erreur: {error_msg}")
        error_message.setAlignment(Qt.AlignCenter)
        error_message.setStyleSheet("font-size: 12px; color: #6B7280; margin: 10px;")
        error_message.setWordWrap(True)
        layout.addWidget(error_message)
        
        # Bouton de rechargement
        reload_btn = QPushButton("🔄 Recharger le module")
        reload_btn.setStyleSheet("""
            QPushButton {
                background: #3B82F6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2563EB;
            }
        """)
        reload_btn.clicked.connect(lambda: self.reload_module(module_id))
        layout.addWidget(reload_btn)
        
        return widget
    
    def reload_module(self, module_id):
        """Recharge un module"""
        if module_id in self.loaded_modules:
            # Supprimer l'ancien module
            old_widget = self.loaded_modules[module_id]
            self.content_stack.removeWidget(old_widget)
            old_widget.deleteLater()
            del self.loaded_modules[module_id]
        
        # Recharger le module
        self.navigate_to_module(module_id)
    
    def get_modules_list(self):
        """Retourne la liste des modules disponibles"""
        return [
            {
                "id": module_id,
                "title": config["title"],
                "icon": config["icon"],
                "description": config["description"]
            }
            for module_id, config in self.modules_config.items()
        ]
    
    def get_current_module(self):
        """Retourne l'ID du module actuellement affiché"""
        if not self.content_stack:
            return None
        
        current_widget = self.content_stack.currentWidget()
        for module_id, widget in self.loaded_modules.items():
            if widget == current_widget:
                return module_id
        
        return None

# Instance globale du navigateur
module_navigator = ModuleNavigator()
