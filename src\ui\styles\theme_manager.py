#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de thèmes moderne pour GSCOM
Support des thèmes sombre, clair et système avec animations fluides
"""

import os
import json
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal
from src.core.config import app_config


class ThemeManager(QObject):
    """Gestionnaire centralisé des thèmes de l'application"""
    
    # Signal émis lors du changement de thème
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        self.themes = {}
        self.load_themes()
    
    def load_themes(self):
        """Charge tous les thèmes disponibles"""
        from .themes.dark_theme import DarkTheme
        from .themes.light_theme import LightTheme
        
        self.themes = {
            "dark": DarkTheme(),
            "light": LightTheme(),
            "system": self.get_system_theme()
        }
        
        # Charger le thème depuis la configuration
        saved_theme = app_config.get('ui.theme', 'dark')
        self.set_theme(saved_theme)
    
    def get_system_theme(self):
        """Détecte le thème système"""
        try:
            import winreg
            # Vérifier le thème Windows
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize")
            value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            winreg.CloseKey(key)
            
            if value == 0:  # Thème sombre
                from .themes.dark_theme import DarkTheme
                return DarkTheme()
            else:  # Thème clair
                from .themes.light_theme import LightTheme
                return LightTheme()
        except:
            # Par défaut, utiliser le thème sombre
            from .themes.dark_theme import DarkTheme
            return DarkTheme()
    
    def set_theme(self, theme_name: str):
        """Change le thème actuel"""
        if theme_name in self.themes:
            self.current_theme = theme_name

            # Sauvegarder dans la configuration
            app_config.set('ui.theme', theme_name)
            app_config.save_config()

            # Émettre le signal de changement
            self.theme_changed.emit(theme_name)
    
    def get_current_theme(self):
        """Retourne le thème actuel"""
        return self.themes.get(self.current_theme)
    
    def get_theme_colors(self) -> Dict[str, str]:
        """Retourne les couleurs du thème actuel"""
        theme = self.get_current_theme()
        return theme.get_colors() if theme else {}
    
    def get_theme_styles(self) -> str:
        """Retourne les styles CSS du thème actuel"""
        theme = self.get_current_theme()
        return theme.get_styles() if theme else ""
    
    def get_available_themes(self) -> list:
        """Retourne la liste des thèmes disponibles"""
        return list(self.themes.keys())
    
    def is_dark_theme(self) -> bool:
        """Vérifie si le thème actuel est sombre"""
        theme = self.get_current_theme()
        return theme.is_dark() if theme else True


class BaseTheme:
    """Classe de base pour tous les thèmes"""
    
    def __init__(self):
        self.name = "base"
        self.colors = {}
        self.styles = ""
    
    def get_colors(self) -> Dict[str, str]:
        """Retourne les couleurs du thème"""
        return self.colors
    
    def get_styles(self) -> str:
        """Retourne les styles CSS du thème"""
        return self.styles
    
    def is_dark(self) -> bool:
        """Indique si le thème est sombre"""
        return True
    
    def get_component_style(self, component: str) -> str:
        """Retourne le style d'un composant spécifique"""
        return ""


# Instance globale du gestionnaire de thèmes
theme_manager = ThemeManager()
