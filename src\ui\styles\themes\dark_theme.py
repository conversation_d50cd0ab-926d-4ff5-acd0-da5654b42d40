#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thème sombre moderne pour GSCOM
Inspiré de Material 3 et Fluent Design avec palette optimisée
"""

from typing import Dict
from .base_theme import BaseTheme


class DarkTheme(BaseTheme):
    """Thème sombre moderne avec design professionnel"""
    
    def __init__(self):
        self.name = "dark"
        self.colors = self.define_colors()
        self.styles = self.define_styles()
    
    def define_colors(self) -> Dict[str, str]:
        """Palette de couleurs pour le thème sombre"""
        return {
            # Couleurs principales - Palette moderne
            'primary': '#00d4ff',           # Cyan électrique
            'primary_dark': '#0099cc',      # <PERSON>an foncé
            'primary_light': '#33ddff',     # Cyan clair
            'secondary': '#8b5cf6',         # Violet moderne
            'secondary_dark': '#7c3aed',    # Violet foncé
            'secondary_light': '#a78bfa',   # <PERSON> clair
            
            # Couleurs de fond - Tons sombres élégants
            'background': '#0f0f23',        # Bleu très sombre
            'background_secondary': '#1a1a2e', # Bleu sombre
            'surface': '#16213e',           # Surface principale
            'surface_variant': '#1e2749',   # Surface variante
            'surface_elevated': '#252d4a',  # Surface élevée
            
            # Couleurs de texte - Contraste optimisé
            'text_primary': '#ffffff',      # Blanc pur
            'text_secondary': '#e2e8f0',    # Gris très clair
            'text_tertiary': '#94a3b8',     # Gris moyen
            'text_inverse': '#0f0f23',      # Texte inversé
            'text_muted': '#64748b',        # Texte atténué
            
            # Couleurs de bordure - Subtiles et élégantes
            'border': 'rgba(255, 255, 255, 0.12)',
            'border_light': 'rgba(255, 255, 255, 0.08)',
            'border_dark': 'rgba(255, 255, 255, 0.16)',
            'border_accent': 'rgba(0, 212, 255, 0.4)',
            
            # États interactifs
            'hover': 'rgba(0, 212, 255, 0.12)',
            'active': 'rgba(0, 212, 255, 0.24)',
            'focus': 'rgba(0, 212, 255, 0.32)',
            'disabled': '#4a5568',
            'selected': 'rgba(0, 212, 255, 0.2)',
            
            # Couleurs sémantiques - Palette moderne
            'success': '#10b981',           # Vert moderne
            'success_light': '#34d399',
            'warning': '#f59e0b',           # Orange moderne
            'warning_light': '#fbbf24',
            'error': '#ef4444',             # Rouge moderne
            'error_light': '#f87171',
            'info': '#3b82f6',              # Bleu info
            'info_light': '#60a5fa',
            
            # Ombres et effets
            'shadow_light': 'rgba(0, 0, 0, 0.1)',
            'shadow_medium': 'rgba(0, 0, 0, 0.2)',
            'shadow_dark': 'rgba(0, 0, 0, 0.4)',
            'glow_primary': 'rgba(0, 212, 255, 0.3)',
            'glow_secondary': 'rgba(139, 92, 246, 0.3)',
            
            # Dégradés
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00d4ff, stop:1 #8b5cf6)',
            'gradient_surface': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e2749, stop:1 #16213e)',
            'gradient_button': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #252d4a, stop:1 #1e2749)',
        }
    
    def define_styles(self) -> str:
        """Styles CSS complets pour le thème sombre"""
        return f"""
        /* === STYLES GLOBAUX === */
        QMainWindow {{
            background: {self.colors['background']};
            color: {self.colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'SF Pro Display', system-ui, sans-serif;
        }}
        
        QWidget {{
            background: transparent;
            color: {self.colors['text_primary']};
        }}
        
        /* === NAVIGATION SIDEBAR === */
        #sidebar {{
            background: {self.colors['gradient_surface']};
            border-right: 1px solid {self.colors['border_dark']};
            border-radius: 0;
        }}
        
        #sidebarHeader {{
            background: {self.colors['surface_elevated']};
            border-bottom: 1px solid {self.colors['border']};
            border-radius: 0 0 16px 16px;
            padding: 20px;
        }}
        
        #logoLabel {{
            background: {self.colors['gradient_primary']};
            border: 2px solid {self.colors['border_accent']};
            border-radius: 24px;
            color: {self.colors['text_inverse']};
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }}
        
        #titleLabel {{
            color: {self.colors['primary']};
            font-size: 18px;
            font-weight: 700;
            text-shadow: 0 0 10px {self.colors['glow_primary']};
        }}
        
        #subtitleLabel {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: 400;
        }}
        
        /* === BOUTONS DE NAVIGATION === */
        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 14px 16px;
            border-radius: 12px;
            margin: 3px 12px;
            min-height: 48px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        #navButton:hover {{
            background: {self.colors['hover']};
            border-left: 3px solid {self.colors['primary']};
            transform: translateX(2px);
        }}
        
        #navButton:pressed {{
            background: {self.colors['active']};
        }}
        
        #navButton[selected="true"] {{
            background: {self.colors['selected']};
            border-left: 3px solid {self.colors['primary']};
        }}
        
        #navIcon {{
            color: {self.colors['primary']};
            font-size: 20px;
            font-weight: bold;
        }}
        
        #navTitle {{
            color: {self.colors['text_secondary']};
            font-size: 15px;
            font-weight: 600;
        }}
        
        /* === ZONE DE CONTENU === */
        #contentArea {{
            background: {self.colors['background_secondary']};
            border-radius: 16px 0 0 0;
        }}
        
        /* === CARTES ET SURFACES === */
        .card, QFrame[class="card"] {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 16px;
            padding: 20px;
        }}
        
        .card:hover {{
            border-color: {self.colors['border_accent']};
            box-shadow: 0 8px 32px {self.colors['shadow_medium']};
        }}
        """
    
    def is_dark(self) -> bool:
        """Ce thème est sombre"""
        return True
