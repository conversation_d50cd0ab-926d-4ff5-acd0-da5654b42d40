#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thème clair moderne pour GSCOM
Inspiré de Material 3 et Windows 11 avec palette optimisée
"""

from typing import Dict
from .base_theme import BaseTheme


class LightTheme(BaseTheme):
    """Thème clair moderne avec design professionnel"""
    
    def __init__(self):
        self.name = "light"
        self.colors = self.define_colors()
        self.styles = self.define_styles()
    
    def define_colors(self) -> Dict[str, str]:
        """Palette de couleurs pour le thème clair"""
        return {
            # Couleurs principales - Palette moderne
            'primary': '#0066cc',           # Bleu professionnel
            'primary_dark': '#004499',      # Bleu foncé
            'primary_light': '#3388dd',     # Bleu clair
            'secondary': '#6366f1',         # Violet moderne
            'secondary_dark': '#4f46e5',    # Violet foncé
            'secondary_light': '#8b5cf6',   # Violet clair
            
            # Couleurs de fond - Tons clairs élégants
            'background': '#ffffff',        # Blanc pur
            'background_secondary': '#f8fafc', # Gris très clair
            'surface': '#ffffff',           # Surface principale
            'surface_variant': '#f1f5f9',   # Surface variante
            'surface_elevated': '#ffffff',  # Surface élevée
            
            # Couleurs de texte - Contraste optimisé
            'text_primary': '#1e293b',      # Gris très foncé
            'text_secondary': '#475569',    # Gris foncé
            'text_tertiary': '#64748b',     # Gris moyen
            'text_inverse': '#ffffff',      # Texte inversé
            'text_muted': '#94a3b8',        # Texte atténué
            
            # Couleurs de bordure - Subtiles et élégantes
            'border': '#e2e8f0',
            'border_light': '#f1f5f9',
            'border_dark': '#cbd5e1',
            'border_accent': '#0066cc',
            
            # États interactifs
            'hover': 'rgba(0, 102, 204, 0.08)',
            'active': 'rgba(0, 102, 204, 0.16)',
            'focus': 'rgba(0, 102, 204, 0.24)',
            'disabled': '#94a3b8',
            'selected': 'rgba(0, 102, 204, 0.12)',
            
            # Couleurs sémantiques - Palette moderne
            'success': '#059669',           # Vert moderne
            'success_light': '#10b981',
            'warning': '#d97706',           # Orange moderne
            'warning_light': '#f59e0b',
            'error': '#dc2626',             # Rouge moderne
            'error_light': '#ef4444',
            'info': '#2563eb',              # Bleu info
            'info_light': '#3b82f6',
            
            # Ombres et effets
            'shadow_light': 'rgba(0, 0, 0, 0.05)',
            'shadow_medium': 'rgba(0, 0, 0, 0.1)',
            'shadow_dark': 'rgba(0, 0, 0, 0.15)',
            'glow_primary': 'rgba(0, 102, 204, 0.2)',
            'glow_secondary': 'rgba(99, 102, 241, 0.2)',
            
            # Dégradés
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0066cc, stop:1 #6366f1)',
            'gradient_surface': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8fafc)',
            'gradient_button': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f1f5f9)',
        }
    
    def define_styles(self) -> str:
        """Styles CSS complets pour le thème clair"""
        return f"""
        /* === STYLES GLOBAUX === */
        QMainWindow {{
            background: {self.colors['background']};
            color: {self.colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'SF Pro Display', system-ui, sans-serif;
        }}
        
        QWidget {{
            background: transparent;
            color: {self.colors['text_primary']};
        }}
        
        /* === NAVIGATION SIDEBAR === */
        #sidebar {{
            background: {self.colors['surface']};
            border-right: 1px solid {self.colors['border']};
            border-radius: 0;
        }}
        
        #sidebarHeader {{
            background: {self.colors['gradient_surface']};
            border-bottom: 1px solid {self.colors['border']};
            border-radius: 0 0 16px 16px;
            padding: 20px;
        }}
        
        #logoLabel {{
            background: {self.colors['gradient_primary']};
            border: 2px solid {self.colors['border_accent']};
            border-radius: 24px;
            color: {self.colors['text_inverse']};
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }}
        
        #titleLabel {{
            color: {self.colors['primary']};
            font-size: 18px;
            font-weight: 700;
        }}
        
        #subtitleLabel {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: 400;
        }}
        
        /* === BOUTONS DE NAVIGATION === */
        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 14px 16px;
            border-radius: 12px;
            margin: 3px 12px;
            min-height: 48px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        #navButton:hover {{
            background: {self.colors['hover']};
            border-left: 3px solid {self.colors['primary']};
            transform: translateX(2px);
        }}
        
        #navButton:pressed {{
            background: {self.colors['active']};
        }}
        
        #navButton[selected="true"] {{
            background: {self.colors['selected']};
            border-left: 3px solid {self.colors['primary']};
        }}
        
        #navIcon {{
            color: {self.colors['primary']};
            font-size: 20px;
            font-weight: bold;
        }}
        
        #navTitle {{
            color: {self.colors['text_secondary']};
            font-size: 15px;
            font-weight: 600;
        }}
        
        /* === ZONE DE CONTENU === */
        #contentArea {{
            background: {self.colors['background_secondary']};
            border-radius: 16px 0 0 0;
        }}
        
        /* === CARTES ET SURFACES === */
        .card, QFrame[class="card"] {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px {self.colors['shadow_light']};
        }}
        
        .card:hover {{
            border-color: {self.colors['border_accent']};
            box-shadow: 0 8px 32px {self.colors['shadow_medium']};
        }}
        """
    
    def is_dark(self) -> bool:
        """Ce thème est clair"""
        return False
