#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thème clair moderne pour GSCOM
Inspiré de Material 3 et Windows 11 avec palette optimisée
"""

from typing import Dict
from .base_theme import BaseTheme


class LightTheme(BaseTheme):
    """Thème clair moderne avec design professionnel"""
    
    def __init__(self):
        self.name = "light"
        self.colors = self.define_colors()
        self.styles = self.define_styles()
    
    def define_colors(self) -> Dict[str, str]:
        """Palette de couleurs pour le thème clair avec contraste amélioré"""
        return {
            # Couleurs principales - Palette moderne avec contraste élevé
            'primary': '#1565c0',           # Bleu professionnel plus foncé
            'primary_dark': '#0d47a1',      # Bleu très foncé
            'primary_light': '#42a5f5',     # Bleu clair
            'secondary': '#5e35b1',         # Violet moderne
            'secondary_dark': '#4527a0',    # Violet foncé
            'secondary_light': '#7e57c2',   # Violet clair

            # Couleurs de fond - Tons clairs avec plus de contraste
            'background': '#fafafa',        # Gris très clair (pas blanc pur)
            'background_secondary': '#f5f5f5', # Gris clair
            'surface': '#ffffff',           # Surface principale blanche
            'surface_variant': '#eceff1',   # Surface variante
            'surface_elevated': '#ffffff',  # Surface élevée

            # Couleurs de texte - Contraste WCAG AAA
            'text_primary': '#212121',      # Noir presque pur
            'text_secondary': '#424242',    # Gris très foncé
            'text_tertiary': '#616161',     # Gris foncé
            'text_inverse': '#ffffff',      # Texte inversé
            'text_muted': '#757575',        # Texte atténué

            # Couleurs de bordure - Plus visibles
            'border': '#e0e0e0',            # Bordure standard
            'border_light': '#eeeeee',      # Bordure claire
            'border_dark': '#bdbdbd',       # Bordure foncée
            'border_accent': '#1565c0',     # Bordure accent

            # États interactifs - Plus visibles
            'hover': 'rgba(21, 101, 192, 0.08)',
            'active': 'rgba(21, 101, 192, 0.16)',
            'focus': 'rgba(21, 101, 192, 0.24)',
            'disabled': '#9e9e9e',
            'selected': 'rgba(21, 101, 192, 0.12)',

            # Couleurs sémantiques - Palette moderne
            'success': '#2e7d32',           # Vert foncé
            'success_light': '#4caf50',
            'warning': '#f57c00',           # Orange foncé
            'warning_light': '#ff9800',
            'error': '#d32f2f',             # Rouge foncé
            'error_light': '#f44336',
            'info': '#1976d2',              # Bleu info foncé
            'info_light': '#2196f3',

            # Ombres et effets - Plus prononcés
            'shadow_light': 'rgba(0, 0, 0, 0.08)',
            'shadow_medium': 'rgba(0, 0, 0, 0.16)',
            'shadow_dark': 'rgba(0, 0, 0, 0.24)',
            'glow_primary': 'rgba(21, 101, 192, 0.3)',
            'glow_secondary': 'rgba(94, 53, 177, 0.3)',

            # Dégradés - Plus contrastés
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1565c0, stop:1 #5e35b1)',
            'gradient_surface': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f5f5f5)',
            'gradient_button': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #eceff1)',
        }
    
    def define_styles(self) -> str:
        """Styles CSS complets pour le thème clair avec contraste amélioré"""
        return f"""
        /* === STYLES GLOBAUX === */
        QMainWindow {{
            background: {self.colors['background']};
            color: {self.colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'SF Pro Display', system-ui, sans-serif;
            font-weight: 400;
        }}

        QWidget {{
            background: transparent;
            color: {self.colors['text_primary']};
        }}

        QLabel {{
            color: {self.colors['text_primary']};
        }}

        /* === NAVIGATION SIDEBAR === */
        #sidebar {{
            background: {self.colors['surface']};
            border-right: 2px solid {self.colors['border_dark']};
            border-radius: 0;
        }}

        #sidebarHeader {{
            background: {self.colors['surface_variant']};
            border-bottom: 2px solid {self.colors['border']};
            border-radius: 0 0 16px 16px;
            padding: 20px;
        }}

        #logoLabel {{
            background: {self.colors['gradient_primary']};
            border: 3px solid {self.colors['primary']};
            border-radius: 30px;
            color: {self.colors['text_inverse']};
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }}

        #titleLabel {{
            color: {self.colors['primary']};
            font-size: 18px;
            font-weight: 700;
        }}

        #subtitleLabel {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: 500;
        }}

        /* === BOUTONS DE NAVIGATION === */
        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 14px 16px;
            border-radius: 12px;
            margin: 3px 12px;
            min-height: 48px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}

        #navButton:hover {{
            background: {self.colors['hover']};
            border-left: 4px solid {self.colors['primary']};
            transform: translateX(2px);
        }}

        #navButton:pressed {{
            background: {self.colors['active']};
        }}

        #navButton[selected="true"] {{
            background: {self.colors['selected']};
            border-left: 4px solid {self.colors['primary']};
        }}

        #navIcon {{
            color: {self.colors['primary']};
            font-size: 20px;
            font-weight: bold;
        }}

        #navTitle {{
            color: {self.colors['text_primary']};
            font-size: 15px;
            font-weight: 600;
        }}

        /* === ZONE UTILISATEUR === */
        #userInfo {{
            background: {self.colors['surface_variant']};
            border: 2px solid {self.colors['border']};
            border-radius: 12px;
            margin: 12px;
        }}

        #userName {{
            color: {self.colors['primary']};
            font-size: 14px;
            font-weight: bold;
        }}

        #userActionButton {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['border']};
            border-radius: 8px;
            color: {self.colors['text_primary']};
            font-size: 14px;
            font-weight: 600;
        }}

        #userActionButton:hover {{
            background: {self.colors['hover']};
            border-color: {self.colors['primary']};
            color: {self.colors['primary']};
        }}

        /* === ZONE DE CONTENU === */
        #contentArea {{
            background: {self.colors['background_secondary']};
            border-radius: 16px 0 0 0;
        }}

        /* === CARTES ET SURFACES === */
        .card, QFrame[class="card"] {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['border']};
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px {self.colors['shadow_light']};
        }}

        .card:hover {{
            border-color: {self.colors['primary']};
            box-shadow: 0 8px 32px {self.colors['shadow_medium']};
        }}

        /* === SCROLLBARS === */
        #navScroll {{
            background: transparent;
            border: none;
        }}

        #navScroll QScrollBar:vertical {{
            background: {self.colors['surface_variant']};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        #navScroll QScrollBar::handle:vertical {{
            background: {self.colors['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}

        #navScroll QScrollBar::handle:vertical:hover {{
            background: {self.colors['primary_dark']};
        }}

        #navScroll QScrollBar::add-line:vertical,
        #navScroll QScrollBar::sub-line:vertical {{
            height: 0;
            background: transparent;
        }}
        """
    
    def is_dark(self) -> bool:
        """Ce thème est clair"""
        return False
