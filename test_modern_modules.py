#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des Modules Modernisés GSCOM
Teste tous les nouveaux modules avec style cohérent au dashboard
"""

import sys
import os
import logging
from datetime import datetime

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MockUser:
    """Utilisateur fictif pour les tests"""
    def __init__(self):
        self.first_name = "Administrateur"
        self.username = "admin"
        self.id = 1

class ModernModulesTestApp(QApplication):
    """Application de test pour les modules modernisés"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setApplicationName("GSCOM Modern Modules Test")
        self.setApplicationVersion("1.0")
        
        # Utilisateur fictif
        self.current_user = MockUser()
        
        self.setup_test_environment()
        self.create_test_interface()
    
    def setup_test_environment(self):
        """Configure l'environnement de test"""
        print("🚀 Démarrage du test Modules Modernisés GSCOM...")
        print("🚀 Configuration de l'environnement de test...")
        
        # Vérifier les dépendances
        try:
            from src.ui.styles.theme_manager import theme_manager
            print("✅ Theme Manager disponible")
            self.theme_manager = theme_manager
        except ImportError as e:
            print(f"⚠️ Theme Manager non disponible: {e}")
            self.create_mock_theme_manager()
        
        # Tester les modules modernisés
        self.test_modules = {}
        
        # Test Dashboard
        try:
            from src.ui.dashboard import MainDashboardInterface
            self.test_modules['dashboard'] = MainDashboardInterface
            print("✅ Dashboard moderne disponible")
        except ImportError as e:
            print(f"❌ Dashboard non disponible: {e}")
        
        # Test Commercial
        try:
            from src.ui.modules.modern_commercial_interface import ModernCommercialInterface
            self.test_modules['commercial'] = ModernCommercialInterface
            print("✅ Module Commercial moderne disponible")
        except ImportError as e:
            print(f"❌ Module Commercial non disponible: {e}")
        
        # Test Clients
        try:
            from src.ui.modules.modern_clients_interface import ModernClientsInterface
            self.test_modules['clients'] = ModernClientsInterface
            print("✅ Module Clients moderne disponible")
        except ImportError as e:
            print(f"❌ Module Clients non disponible: {e}")
        
        # Test Commandes (existant)
        try:
            from src.ui.modules.orders_management_interface import OrdersManagementInterface
            self.test_modules['orders'] = OrdersManagementInterface
            print("✅ Module Commandes moderne disponible")
        except ImportError as e:
            print(f"❌ Module Commandes non disponible: {e}")
        
        return len(self.test_modules) > 0
    
    def create_mock_theme_manager(self):
        """Crée un gestionnaire de thème fictif"""
        class MockThemeManager(QObject):
            theme_changed = pyqtSignal(str)
            
            def __init__(self):
                super().__init__()
                self.current_theme = "light"
            
            def set_theme(self, theme_name):
                print(f"🎨 Thème changé vers: {theme_name}")
                self.current_theme = theme_name
                self.theme_changed.emit(theme_name)
        
        # Créer le module fictif
        import types
        theme_module = types.ModuleType('theme_manager')
        theme_module.theme_manager = MockThemeManager()
        self.theme_manager = theme_module.theme_manager
        
        # L'ajouter aux modules système
        sys.modules['src.ui.styles.theme_manager'] = theme_module
    
    def create_test_interface(self):
        """Crée l'interface de test avec onglets"""
        try:
            # Fenêtre principale
            self.main_window = QMainWindow()
            self.main_window.setWindowTitle("Test Modules Modernisés GSCOM")
            self.main_window.setMinimumSize(1600, 1000)
            self.main_window.resize(1800, 1200)
            
            # Widget central avec onglets
            central_widget = QWidget()
            self.main_window.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(10, 10, 10, 10)
            
            # Header de test
            self.create_test_header(layout)
            
            # Onglets pour les modules
            self.tabs = QTabWidget()
            self.tabs.setObjectName("modulesTabs")
            layout.addWidget(self.tabs)
            
            # Créer les onglets pour chaque module
            self.create_module_tabs()
            
            # Appliquer les styles
            self.apply_test_styles()
            
            self.main_window.show()
            
            print("✅ Interface de test créée avec succès")
            self.show_test_info()
            
        except Exception as e:
            print(f"❌ Erreur création interface: {e}")
            import traceback
            traceback.print_exc()
    
    def create_test_header(self, layout):
        """Crée l'en-tête de test"""
        header = QFrame()
        header.setObjectName("testHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 15, 20, 15)
        
        # Titre
        title = QLabel("🧪 Test des Modules Modernisés GSCOM")
        title.setObjectName("testTitle")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Contrôles de thème
        theme_layout = QHBoxLayout()
        
        theme_label = QLabel("Thème:")
        theme_label.setObjectName("themeLabel")
        theme_layout.addWidget(theme_label)
        
        # Boutons de thème
        light_btn = QPushButton("☀️ Clair")
        light_btn.setObjectName("themeButton")
        light_btn.clicked.connect(lambda: self.change_theme("light"))
        theme_layout.addWidget(light_btn)
        
        dark_btn = QPushButton("🌙 Sombre")
        dark_btn.setObjectName("themeButton")
        dark_btn.clicked.connect(lambda: self.change_theme("dark"))
        theme_layout.addWidget(dark_btn)
        
        system_btn = QPushButton("🖥️ Système")
        system_btn.setObjectName("themeButton")
        system_btn.clicked.connect(lambda: self.change_theme("system"))
        theme_layout.addWidget(system_btn)
        
        header_layout.addLayout(theme_layout)
        layout.addWidget(header)
    
    def create_module_tabs(self):
        """Crée les onglets pour chaque module"""
        for module_name, module_class in self.test_modules.items():
            try:
                # Créer l'instance du module
                if module_name == 'dashboard':
                    # Dashboard nécessite un traitement spécial
                    module_instance = module_class(self.current_user)
                    # Extraire le widget central
                    module_widget = module_instance.centralWidget()
                else:
                    # Autres modules
                    module_instance = module_class(self.current_user)
                    module_widget = module_instance.centralWidget()
                
                # Ajouter l'onglet
                tab_title = self.get_tab_title(module_name)
                self.tabs.addTab(module_widget, tab_title)
                
                print(f"✅ Onglet {module_name} créé")
                
            except Exception as e:
                print(f"❌ Erreur création onglet {module_name}: {e}")
                
                # Créer un onglet d'erreur
                error_widget = self.create_error_tab(module_name, str(e))
                tab_title = f"❌ {self.get_tab_title(module_name)}"
                self.tabs.addTab(error_widget, tab_title)
    
    def get_tab_title(self, module_name):
        """Retourne le titre de l'onglet pour un module"""
        titles = {
            'dashboard': '📊 Dashboard',
            'commercial': '💼 Commercial',
            'clients': '👥 Clients',
            'orders': '📋 Commandes',
            'products': '📦 Produits',
            'invoices': '🧾 Factures'
        }
        return titles.get(module_name, f"📄 {module_name.title()}")
    
    def create_error_tab(self, module_name, error_msg):
        """Crée un onglet d'erreur"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Icône d'erreur
        error_icon = QLabel("⚠️")
        error_icon.setAlignment(Qt.AlignCenter)
        error_icon.setStyleSheet("font-size: 48px; color: #F59E0B; margin: 20px;")
        layout.addWidget(error_icon)
        
        # Titre d'erreur
        error_title = QLabel(f"Module {module_name.title()} indisponible")
        error_title.setAlignment(Qt.AlignCenter)
        error_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #374151; margin: 10px;")
        layout.addWidget(error_title)
        
        # Message d'erreur
        error_message = QLabel(f"Erreur: {error_msg}")
        error_message.setAlignment(Qt.AlignCenter)
        error_message.setStyleSheet("font-size: 12px; color: #6B7280; margin: 10px;")
        error_message.setWordWrap(True)
        layout.addWidget(error_message)
        
        return widget
    
    def change_theme(self, theme_name):
        """Change le thème de tous les modules"""
        print(f"🎨 Changement de thème vers: {theme_name}")
        self.theme_manager.set_theme(theme_name)
    
    def apply_test_styles(self):
        """Applique les styles à l'interface de test"""
        self.main_window.setStyleSheet("""
            QMainWindow {
                background: #F8FAFC;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
            
            #testHeader {
                background: white;
                border-bottom: 1px solid #E5E7EB;
                border-radius: 8px;
                margin-bottom: 10px;
            }
            
            #testTitle {
                font-size: 20px;
                font-weight: 700;
                color: #1F2937;
            }
            
            #themeLabel {
                font-size: 14px;
                font-weight: 500;
                color: #6B7280;
                margin-right: 10px;
            }
            
            #themeButton {
                background: #3B82F6;
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 12px;
                font-weight: 600;
                padding: 8px 12px;
                margin: 0 2px;
            }
            
            #themeButton:hover {
                background: #2563EB;
            }
            
            #modulesTabs {
                background: white;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
            }
            
            #modulesTabs::pane {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background: white;
            }
            
            #modulesTabs::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-bottom: none;
                border-radius: 6px 6px 0 0;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: 500;
                color: #6B7280;
            }
            
            QTabBar::tab:selected {
                background: white;
                color: #1F2937;
                border-bottom: 2px solid #3B82F6;
            }
            
            QTabBar::tab:hover {
                background: #F3F4F6;
                color: #374151;
            }
        """)
    
    def show_test_info(self):
        """Affiche les informations de test"""
        print("\n" + "="*80)
        print("🧪 TEST DES MODULES MODERNISÉS - INTERFACE GSCOM")
        print("="*80)
        print(f"📅 Démarré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
        print(f"👤 Utilisateur: {self.current_user.first_name}")
        print(f"🖥️ Résolution: {self.primaryScreen().size().width()}x{self.primaryScreen().size().height()}")
        print(f"📋 Modules testés: {len(self.test_modules)}")
        
        print("\n🎯 MODULES DISPONIBLES:")
        for i, (module_name, module_class) in enumerate(self.test_modules.items(), 1):
            print(f"  {i:2d}. {self.get_tab_title(module_name)} - {module_class.__name__}")
        
        print("\n🎨 TESTS DE COHÉRENCE VISUELLE:")
        print("  ✅ Style unifié avec le dashboard")
        print("  ✅ Couleurs cohérentes (#F8FAFC, #3B82F6, #00BCD4)")
        print("  ✅ Typographie Inter/Segoe UI")
        print("  ✅ Cartes avec bordures arrondies")
        print("  ✅ Thèmes clair/sombre/système")
        
        print("\n🧪 INSTRUCTIONS DE TEST:")
        print("  1. Naviguez entre les onglets des modules")
        print("  2. Testez les changements de thème (☀️ 🌙 🖥️)")
        print("  3. Vérifiez la cohérence visuelle")
        print("  4. Testez les interactions dans chaque module")
        print("  5. Validez la responsivité")
        
        print("\n🎮 CONTRÔLES DISPONIBLES:")
        print("  • Onglets pour navigation entre modules")
        print("  • Boutons de thème en haut à droite")
        print("  • Interactions spécifiques à chaque module")
        print("="*80)

def main():
    """Fonction principale"""
    print("🎨 Démarrage du test Modules Modernisés GSCOM...")
    print("🎯 Objectif: Tester la cohérence et fonctionnalité des modules")
    
    # Créer l'application
    app = ModernModulesTestApp(sys.argv)
    
    print("✅ Application de test créée")
    print("🎬 Interface de test en cours...")
    
    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
