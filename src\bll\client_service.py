#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service pour la gestion des clients et fournisseurs
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import or_, and_

from src.bll.base_service import BaseService
from src.bll.supplier_service import SupplierService
from src.dal.models.client import Client, Supplier, ClientType, PaymentTerms
from src.dal.database import db_manager

class ClientService(BaseService):
    """Service pour la gestion des clients et fournisseurs"""

    def __init__(self):
        super().__init__(Client)
        self.logger = logging.getLogger(__name__)
        self.supplier_service = SupplierService()

    def get_active_clients(self, session: Optional[Session] = None) -> List[Client]:
        """Récupère tous les clients actifs"""
        return self.get_all(session=session, is_active=True)

    def get_vip_clients(self, session: Optional[Session] = None) -> List[Client]:
        """Récupère tous les clients VIP"""
        return self.get_all(session=session, is_vip=True, is_active=True)

    def search_clients(self, search_term: str, session: Optional[Session] = None) -> List[Client]:
        """Recherche des clients par nom, code, email ou téléphone"""
        try:
            if session:
                query = session.query(Client)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Client)

                    # Construire la condition de recherche
                    search_conditions = [
                        Client.name.ilike(f"%{search_term}%"),
                        Client.company_name.ilike(f"%{search_term}%"),
                        Client.code.ilike(f"%{search_term}%"),
                        Client.email.ilike(f"%{search_term}%"),
                        Client.phone.ilike(f"%{search_term}%"),
                        Client.mobile.ilike(f"%{search_term}%")
                    ]

                    query = query.filter(or_(*search_conditions))
                    return query.all()

            # Si session fournie
            search_conditions = [
                Client.name.ilike(f"%{search_term}%"),
                Client.company_name.ilike(f"%{search_term}%"),
                Client.code.ilike(f"%{search_term}%"),
                Client.email.ilike(f"%{search_term}%"),
                Client.phone.ilike(f"%{search_term}%"),
                Client.mobile.ilike(f"%{search_term}%")
            ]

            query = query.filter(or_(*search_conditions))
            return query.all()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la recherche de clients: {e}")
            return []

    def get_clients_by_type(self, client_type: ClientType, session: Optional[Session] = None) -> List[Client]:
        """Récupère les clients par type"""
        return self.get_all(session=session, client_type=client_type)

    def get_clients_by_city(self, city: str, session: Optional[Session] = None) -> List[Client]:
        """Récupère les clients par ville"""
        return self.get_all(session=session, city=city)

    def get_client_by_code(self, code: str, session: Optional[Session] = None) -> Optional[Client]:
        """Récupère un client par son code"""
        return self.get_by_field('code', code, session)

    def get_client_by_email(self, email: str, session: Optional[Session] = None) -> Optional[Client]:
        """Récupère un client par son email"""
        return self.get_by_field('email', email, session)

    def generate_client_code(self, session: Optional[Session] = None) -> str:
        """Génère un nouveau code client"""
        try:
            if session:
                last_client = session.query(Client).order_by(Client.id.desc()).first()
            else:
                with db_manager.get_session() as session:
                    last_client = session.query(Client).order_by(Client.id.desc()).first()

            next_id = (last_client.id + 1) if last_client else 1
            return f"CLI{next_id:06d}"

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la génération du code client: {e}")
            return f"CLI{1:06d}"

    def validate_data(self, data: Dict[str, Any], is_update: bool = False) -> List[str]:
        """Valide les données client"""
        errors = super().validate_data(data, is_update)

        # Validation du nom
        name = data.get('name', '').strip()
        if not name:
            errors.append("Le nom du client est obligatoire")
        elif len(name) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")

        # Validation de l'email
        email = data.get('email', '').strip()
        if email and '@' not in email:
            errors.append("L'adresse email n'est pas valide")

        # Validation du code client (unicité)
        code = data.get('code', '').strip()
        if code:
            try:
                with db_manager.get_session() as session:
                    existing_client = session.query(Client).filter(Client.code == code).first()
                    if existing_client and (not is_update or existing_client.id != data.get('id')):
                        errors.append(f"Le code client '{code}' existe déjà")
            except SQLAlchemyError as e:
                self.logger.error(f"Erreur lors de la validation du code: {e}")

        # Validation de l'email (unicité)
        if email:
            try:
                with db_manager.get_session() as session:
                    existing_client = session.query(Client).filter(Client.email == email).first()
                    if existing_client and (not is_update or existing_client.id != data.get('id')):
                        errors.append(f"L'email '{email}' est déjà utilisé")
            except SQLAlchemyError as e:
                self.logger.error(f"Erreur lors de la validation de l'email: {e}")

        # Validation de la limite de crédit
        credit_limit = data.get('credit_limit', 0)
        if credit_limit and credit_limit < 0:
            errors.append("La limite de crédit ne peut pas être négative")

        return errors

    def get_client_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """Récupère les statistiques des clients"""
        try:
            if session:
                query = session.query(Client)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Client)

                    stats = {
                        'total': query.count(),
                        'active': query.filter(Client.is_active == True).count(),
                        'inactive': query.filter(Client.is_active == False).count(),
                        'vip': query.filter(and_(Client.is_vip == True, Client.is_active == True)).count(),
                        'companies': query.filter(Client.client_type == ClientType.COMPANY).count(),
                        'individuals': query.filter(Client.client_type == ClientType.INDIVIDUAL).count(),
                        'government': query.filter(Client.client_type == ClientType.GOVERNMENT).count()
                    }

                    return stats

            # Si session fournie
            stats = {
                'total': query.count(),
                'active': query.filter(Client.is_active == True).count(),
                'inactive': query.filter(Client.is_active == False).count(),
                'vip': query.filter(and_(Client.is_vip == True, Client.is_active == True)).count(),
                'companies': query.filter(Client.client_type == ClientType.COMPANY).count(),
                'individuals': query.filter(Client.client_type == ClientType.INDIVIDUAL).count(),
                'government': query.filter(Client.client_type == ClientType.GOVERNMENT).count()
            }

            return stats

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}

    # Méthodes pour les fournisseurs (délégation au SupplierService)
    def get_all_suppliers(self, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère tous les fournisseurs"""
        return self.supplier_service.get_all(session=session)

    def get_supplier_by_id(self, supplier_id: int, session: Optional[Session] = None) -> Optional[Supplier]:
        """Récupère un fournisseur par son ID"""
        return self.supplier_service.get_by_id(supplier_id, session=session)

    def create_supplier(self, data: Dict[str, Any]) -> bool:
        """Crée un nouveau fournisseur"""
        return self.supplier_service.create(data)

    def update_supplier(self, supplier_id: int, data: Dict[str, Any]) -> bool:
        """Met à jour un fournisseur"""
        return self.supplier_service.update(supplier_id, data)

    def delete_supplier(self, supplier_id: int) -> bool:
        """Supprime un fournisseur"""
        return self.supplier_service.delete(supplier_id)

class SupplierService(BaseService):
    """Service pour la gestion des fournisseurs"""

    def __init__(self):
        super().__init__(Supplier)
        self.logger = logging.getLogger(__name__)

    def get_active_suppliers(self, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère tous les fournisseurs actifs"""
        return self.get_all(session=session, is_active=True)

    def get_preferred_suppliers(self, session: Optional[Session] = None) -> List[Supplier]:
        """Récupère tous les fournisseurs préférés"""
        return self.get_all(session=session, is_preferred=True, is_active=True)

    def search_suppliers(self, search_term: str, session: Optional[Session] = None) -> List[Supplier]:
        """Recherche des fournisseurs par nom, code, email ou téléphone"""
        try:
            if session:
                query = session.query(Supplier)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Supplier)

                    # Construire la condition de recherche
                    search_conditions = [
                        Supplier.name.ilike(f"%{search_term}%"),
                        Supplier.company_name.ilike(f"%{search_term}%"),
                        Supplier.code.ilike(f"%{search_term}%"),
                        Supplier.email.ilike(f"%{search_term}%"),
                        Supplier.phone.ilike(f"%{search_term}%"),
                        Supplier.mobile.ilike(f"%{search_term}%")
                    ]

                    query = query.filter(or_(*search_conditions))
                    return query.all()

            # Si session fournie
            search_conditions = [
                Supplier.name.ilike(f"%{search_term}%"),
                Supplier.company_name.ilike(f"%{search_term}%"),
                Supplier.code.ilike(f"%{search_term}%"),
                Supplier.email.ilike(f"%{search_term}%"),
                Supplier.phone.ilike(f"%{search_term}%"),
                Supplier.mobile.ilike(f"%{search_term}%")
            ]

            query = query.filter(or_(*search_conditions))
            return query.all()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la recherche de fournisseurs: {e}")
            return []

    def get_supplier_by_code(self, code: str, session: Optional[Session] = None) -> Optional[Supplier]:
        """Récupère un fournisseur par son code"""
        return self.get_by_field('code', code, session)

    def generate_supplier_code(self, session: Optional[Session] = None) -> str:
        """Génère un nouveau code fournisseur"""
        try:
            if session:
                last_supplier = session.query(Supplier).order_by(Supplier.id.desc()).first()
            else:
                with db_manager.get_session() as session:
                    last_supplier = session.query(Supplier).order_by(Supplier.id.desc()).first()

            next_id = (last_supplier.id + 1) if last_supplier else 1
            return f"FOU{next_id:06d}"

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la génération du code fournisseur: {e}")
            return f"FOU{1:06d}"

    def validate_data(self, data: Dict[str, Any], is_update: bool = False) -> List[str]:
        """Valide les données fournisseur"""
        errors = super().validate_data(data, is_update)

        # Validation du nom
        name = data.get('name', '').strip()
        if not name:
            errors.append("Le nom du fournisseur est obligatoire")
        elif len(name) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")

        # Validation de l'email
        email = data.get('email', '').strip()
        if email and '@' not in email:
            errors.append("L'adresse email n'est pas valide")

        # Validation du code fournisseur (unicité)
        code = data.get('code', '').strip()
        if code:
            try:
                with db_manager.get_session() as session:
                    existing_supplier = session.query(Supplier).filter(Supplier.code == code).first()
                    if existing_supplier and (not is_update or existing_supplier.id != data.get('id')):
                        errors.append(f"Le code fournisseur '{code}' existe déjà")
            except SQLAlchemyError as e:
                self.logger.error(f"Erreur lors de la validation du code: {e}")

        # Validation de la commande minimum
        minimum_order = data.get('minimum_order', 0)
        if minimum_order and minimum_order < 0:
            errors.append("La commande minimum ne peut pas être négative")

        # Validation du délai de livraison
        delivery_time = data.get('delivery_time', 0)
        if delivery_time and delivery_time < 0:
            errors.append("Le délai de livraison ne peut pas être négatif")

        return errors

    def get_supplier_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """Récupère les statistiques des fournisseurs"""
        try:
            if session:
                query = session.query(Supplier)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Supplier)

                    stats = {
                        'total': query.count(),
                        'active': query.filter(Supplier.is_active == True).count(),
                        'inactive': query.filter(Supplier.is_active == False).count(),
                        'preferred': query.filter(and_(Supplier.is_preferred == True, Supplier.is_active == True)).count()
                    }

                    return stats

            # Si session fournie
            stats = {
                'total': query.count(),
                'active': query.filter(Supplier.is_active == True).count(),
                'inactive': query.filter(Supplier.is_active == False).count(),
                'preferred': query.filter(and_(Supplier.is_preferred == True, Supplier.is_active == True)).count()
            }

            return stats

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
