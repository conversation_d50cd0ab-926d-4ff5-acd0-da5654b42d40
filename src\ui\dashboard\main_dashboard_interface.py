#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Tableau de Bord Principal GSCOM
Reproduction exacte de la capture d'écran du dashboard
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.styles.theme_manager import theme_manager

class MainDashboardInterface(QMainWindow):
    """Interface du tableau de bord principal GSCOM"""
    
    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        
        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Tableau de Bord")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        self.setup_ui()
        self.apply_styles()
        self.center_window()
        
        # Connecter aux changements de thème
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar gauche
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # Zone de contenu principal
        self.create_main_content()
        main_layout.addWidget(self.main_content)
    
    def create_sidebar(self):
        """Crée la sidebar bleue avec navigation"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("dashboardSidebar")
        self.sidebar.setFixedWidth(250)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo et titre GSCOM
        self.create_logo_section(layout)
        
        # Contrôle de thème
        self.create_theme_control(layout)
        
        # Navigation
        self.create_navigation_menu(layout)
        
        # Section administrateur en bas
        self.create_admin_section(layout)
    
    def create_logo_section(self, layout):
        """Crée la section logo GSCOM"""
        logo_frame = QFrame()
        logo_frame.setObjectName("logoSection")
        
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(20, 30, 20, 20)
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # Icône GSCOM
        logo_icon = QLabel("🏢")
        logo_icon.setObjectName("logoIcon")
        logo_icon.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_icon)
        
        # Titre GSCOM
        logo_title = QLabel("GSCOM")
        logo_title.setObjectName("logoTitle")
        logo_title.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_title)
        
        # Sous-titre
        logo_subtitle = QLabel("Gestion Commerciale")
        logo_subtitle.setObjectName("logoSubtitle")
        logo_subtitle.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_subtitle)
        
        layout.addWidget(logo_frame)
    
    def create_theme_control(self, layout):
        """Crée le contrôle de thème"""
        theme_frame = QFrame()
        theme_frame.setObjectName("themeControl")
        
        theme_layout = QVBoxLayout(theme_frame)
        theme_layout.setContentsMargins(20, 10, 20, 10)
        theme_layout.setAlignment(Qt.AlignCenter)
        
        # Label Thème
        theme_label = QLabel("Thème")
        theme_label.setObjectName("themeLabel")
        theme_label.setAlignment(Qt.AlignCenter)
        theme_layout.addWidget(theme_label)
        
        # Dropdown thème système
        theme_dropdown = QComboBox()
        theme_dropdown.setObjectName("themeDropdown")
        theme_dropdown.addItems(["Thème Système"])
        theme_dropdown.currentTextChanged.connect(self.change_theme_from_dropdown)
        theme_layout.addWidget(theme_dropdown)
        
        layout.addWidget(theme_frame)
    
    def create_navigation_menu(self, layout):
        """Crée le menu de navigation"""
        nav_frame = QFrame()
        nav_frame.setObjectName("navigationMenu")
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 20, 10, 20)
        nav_layout.setSpacing(5)
        
        # Modules de navigation
        nav_items = [
            ("📊", "Tableau de bord", "dashboard", True),   # Actif
            ("💼", "Commercial", "commercial", False),
            ("📝", "Devis", "quotes", False),
            ("📋", "Commandes", "orders", False),
            ("🧾", "Factures", "invoices", False),
            ("👥", "Clients", "clients", False),
            ("🏭", "Fournisseurs", "suppliers", False),
            ("📦", "Produits", "products", False)
        ]
        
        for icon, title, module_id, is_active in nav_items:
            btn = self.create_nav_button(icon, title, module_id, is_active)
            nav_layout.addWidget(btn)
        
        layout.addWidget(nav_frame)
        layout.addStretch()
    
    def create_nav_button(self, icon, title, module_id, is_active):
        """Crée un bouton de navigation"""
        button = QPushButton()
        button.setObjectName("navButton")
        if is_active:
            button.setProperty("active", True)
        button.setFixedHeight(45)
        button.clicked.connect(lambda: self.switch_module(module_id))
        
        btn_layout = QHBoxLayout(button)
        btn_layout.setContentsMargins(15, 8, 15, 8)
        btn_layout.setSpacing(12)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        btn_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        btn_layout.addWidget(title_label)
        
        btn_layout.addStretch()
        
        return button
    
    def create_admin_section(self, layout):
        """Crée la section administrateur en bas"""
        admin_frame = QFrame()
        admin_frame.setObjectName("adminSection")
        
        admin_layout = QVBoxLayout(admin_frame)
        admin_layout.setContentsMargins(15, 15, 15, 20)
        admin_layout.setSpacing(5)
        
        # Titre
        admin_title = QLabel("Administrateur Système")
        admin_title.setObjectName("adminTitle")
        admin_title.setAlignment(Qt.AlignCenter)
        admin_layout.addWidget(admin_title)
        
        # Profil
        profile_btn = QPushButton("👤 Profil")
        profile_btn.setObjectName("profileButton")
        profile_btn.clicked.connect(self.open_profile)
        admin_layout.addWidget(profile_btn)
        
        layout.addWidget(admin_frame)
    
    def create_main_content(self):
        """Crée la zone de contenu principal"""
        self.main_content = QFrame()
        self.main_content.setObjectName("mainContent")
        
        layout = QVBoxLayout(self.main_content)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Header avec bienvenue
        self.create_header(layout)
        
        # Zone de contenu
        self.create_content_area(layout)
    
    def create_header(self, layout):
        """Crée l'en-tête avec message de bienvenue"""
        header = QFrame()
        header.setObjectName("contentHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(20)
        
        # Message de bienvenue
        welcome_msg = QLabel("Bienvenue, Administrateur !")
        welcome_msg.setObjectName("welcomeMessage")
        header_layout.addWidget(welcome_msg)
        
        header_layout.addStretch()
        
        # Bouton Paramètres
        settings_btn = QPushButton("⚙️ Paramètres")
        settings_btn.setObjectName("settingsButton")
        settings_btn.clicked.connect(self.open_settings)
        header_layout.addWidget(settings_btn)
        
        layout.addWidget(header)
    
    def create_content_area(self, layout):
        """Crée la zone de contenu avec KPI et sections"""
        content = QFrame()
        content.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(30, 20, 30, 30)
        content_layout.setSpacing(25)
        
        # Cartes KPI horizontales
        self.create_kpi_cards(content_layout)
        
        # Sections Informations et Activité
        self.create_info_sections(content_layout)
        
        # Actions rapides
        self.create_quick_actions(content_layout)
        
        layout.addWidget(content)
    
    def create_kpi_cards(self, layout):
        """Crée les 5 cartes KPI horizontales"""
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiSection")
        
        kpi_layout = QHBoxLayout(kpi_frame)
        kpi_layout.setContentsMargins(0, 0, 0, 0)
        kpi_layout.setSpacing(15)
        
        # Données KPI (5 cartes comme dans la capture)
        kpi_data = [
            ("📊", "Ventes", "125k DA", "#3B82F6"),
            ("📋", "Commandes", "28", "#10B981"),
            ("👥", "Clients", "156", "#F59E0B"),
            ("💰", "Revenus", "87%", "#8B5CF6"),
            ("📦", "Stock", "324", "#EF4444")
        ]
        
        for icon, title, value, color in kpi_data:
            card = self.create_kpi_card(icon, title, value, color)
            kpi_layout.addWidget(card)
        
        layout.addWidget(kpi_frame)
    
    def create_kpi_card(self, icon, title, value, color):
        """Crée une carte KPI"""
        card = QFrame()
        card.setObjectName("kpiCard")
        card.setFixedHeight(100)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 10, 15, 10)
        card_layout.setSpacing(5)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("kpiIcon")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        card_layout.addWidget(icon_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("kpiValue")
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        return card

    def create_info_sections(self, layout):
        """Crée les sections Informations Système et Activité Récente"""
        sections_frame = QFrame()
        sections_layout = QHBoxLayout(sections_frame)
        sections_layout.setContentsMargins(0, 0, 0, 0)
        sections_layout.setSpacing(20)

        # Section Informations Système
        info_section = self.create_info_system_section()
        sections_layout.addWidget(info_section)

        # Section Activité Récente
        activity_section = self.create_activity_section()
        sections_layout.addWidget(activity_section)

        layout.addWidget(sections_frame)

    def create_info_system_section(self):
        """Crée la section Informations Système"""
        section = QFrame()
        section.setObjectName("infoSection")

        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(10)

        # Titre avec icône
        title_layout = QHBoxLayout()
        title_icon = QLabel("💻")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)

        title_label = QLabel("Informations Système")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        section_layout.addLayout(title_layout)

        # Informations système
        info_items = [
            ("💾", "Espace disque", "85% utilisé"),
            ("🖥️", "Mémoire RAM", "4.2 GB / 8 GB"),
            ("⚡", "CPU", "Intel Core i5"),
            ("🌐", "Réseau", "Connecté"),
            ("🔄", "Dernière sauvegarde", "Il y a 2h")
        ]

        for icon, label, value in info_items:
            item_layout = QHBoxLayout()

            item_icon = QLabel(icon)
            item_icon.setObjectName("infoIcon")
            item_layout.addWidget(item_icon)

            item_label = QLabel(label)
            item_label.setObjectName("infoLabel")
            item_layout.addWidget(item_label)

            item_layout.addStretch()

            item_value = QLabel(value)
            item_value.setObjectName("infoValue")
            item_layout.addWidget(item_value)

            section_layout.addLayout(item_layout)

        return section

    def create_activity_section(self):
        """Crée la section Activité Récente"""
        section = QFrame()
        section.setObjectName("activitySection")

        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(10)

        # Titre avec icône
        title_layout = QHBoxLayout()
        title_icon = QLabel("📈")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)

        title_label = QLabel("Activité Récente")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        section_layout.addLayout(title_layout)

        # Activités récentes
        activities = [
            ("📋", "Nouvelle commande #CMD-001", "Il y a 5 min"),
            ("👤", "Client ajouté: Société ABC", "Il y a 15 min"),
            ("💰", "Facture #FAC-125 payée", "Il y a 1h"),
            ("📦", "Stock mis à jour", "Il y a 2h"),
            ("📊", "Rapport généré", "Il y a 3h")
        ]

        for icon, activity, time in activities:
            item_layout = QHBoxLayout()

            activity_icon = QLabel(icon)
            activity_icon.setObjectName("activityIcon")
            item_layout.addWidget(activity_icon)

            activity_text = QLabel(activity)
            activity_text.setObjectName("activityText")
            item_layout.addWidget(activity_text)

            item_layout.addStretch()

            activity_time = QLabel(time)
            activity_time.setObjectName("activityTime")
            item_layout.addWidget(activity_time)

            section_layout.addLayout(item_layout)

        return section

    def create_quick_actions(self, layout):
        """Crée la section Actions Rapides (grille 4x2)"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsSection")

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(15)

        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("⚡")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)

        title_label = QLabel("Actions Rapides")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        actions_layout.addLayout(title_layout)

        # Grille d'actions 4x2
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)

        # Actions (comme dans la capture d'écran)
        actions = [
            ("👤", "Nouveau Client", "clients", "#E8F4FD"),
            ("🧾", "Nouvelle Facture", "invoices", "#FFF2E8"),
            ("👥", "Gestion Clients", "clients", "#E8F4FD"),
            ("📊", "Rapports", "reports", "#F0E8FF"),
            ("📋", "Nouvelle Commande", "orders", "#E8F8F0"),
            ("📦", "Nouveau Produit", "products", "#FFF8E8"),
            ("📈", "Statistiques", "stats", "#F0E8FF"),
            ("⚙️", "Paramètres", "settings", "#F8E8E8")
        ]

        for i, (icon, title, action_id, bg_color) in enumerate(actions):
            row = i // 4
            col = i % 4

            action_card = self.create_action_card(icon, title, action_id, bg_color)
            grid_layout.addWidget(action_card, row, col)

        actions_layout.addLayout(grid_layout)
        layout.addWidget(actions_frame)

    def create_action_card(self, icon, title, action_id, bg_color):
        """Crée une carte d'action rapide"""
        card = QPushButton()
        card.setObjectName("actionCard")
        card.setFixedHeight(80)
        card.clicked.connect(lambda: self.execute_action(action_id))
        card.setStyleSheet(f"background-color: {bg_color};")

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 10, 15, 10)
        card_layout.setSpacing(5)
        card_layout.setAlignment(Qt.AlignCenter)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("actionIcon")
        icon_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("actionTitle")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)

        return card

    # === MÉTHODES DE STYLE ===

    def apply_styles(self):
        """Applique les styles CSS selon le thème"""
        self.setStyleSheet(self.get_dashboard_styles())

    def get_dashboard_styles(self):
        """Retourne les styles CSS pour le dashboard selon le thème"""
        try:
            current_theme = theme_manager.current_theme
        except:
            current_theme = "light"

        if current_theme == "dark":
            return self.get_dark_theme_styles()
        else:
            # Thèmes 'light' et 'system' = style capture d'écran
            return self.get_light_theme_styles()

    def get_light_theme_styles(self):
        """Styles pour thème clair - Reproduction exacte de la capture d'écran"""
        return """
        /* === CONFIGURATION GLOBALE === */
        QMainWindow {
            background: #F8FAFC;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === SIDEBAR BLEUE (Exactement comme la capture) === */
        #dashboardSidebar {
            background: #1E40AF;
            border: none;
        }

        #logoSection {
            background: transparent;
        }

        #logoIcon {
            font-size: 32px;
            color: white;
            font-weight: bold;
        }

        #logoTitle {
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin: 5px 0;
        }

        #logoSubtitle {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
        }

        #themeControl {
            background: transparent;
        }

        #themeLabel {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }

        #themeDropdown {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 12px;
            padding: 5px 10px;
        }

        #themeDropdown::drop-down {
            border: none;
        }

        #themeDropdown::down-arrow {
            image: none;
            border: none;
        }

        #navigationMenu {
            background: transparent;
        }

        #navButton {
            background: transparent;
            border: none;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            text-align: left;
            padding: 8px;
        }

        #navButton:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        #navButton[active="true"] {
            background: rgba(255, 255, 255, 0.15);
            border-left: 3px solid white;
            border-radius: 0 8px 8px 0;
        }

        #navIcon {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
        }

        #navTitle {
            font-size: 14px;
            font-weight: 500;
            color: white;
        }

        #adminSection {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin: 10px;
        }

        #adminTitle {
            font-size: 12px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        #profileButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 12px;
            padding: 8px;
        }

        #profileButton:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* === CONTENU PRINCIPAL === */
        #mainContent {
            background: #F8FAFC;
        }

        #contentHeader {
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }

        #welcomeMessage {
            font-size: 24px;
            font-weight: 700;
            color: #00BCD4;
        }

        #settingsButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #settingsButton:hover {
            background: #2563EB;
        }

        /* === CARTES KPI === */
        #kpiSection {
            background: transparent;
        }

        #kpiCard {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #kpiIcon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        #kpiValue {
            font-size: 20px;
            font-weight: 700;
            color: #1F2937;
            margin: 3px 0;
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #6B7280;
        }

        /* === SECTIONS INFORMATIONS ET ACTIVITÉ === */
        #infoSection, #activitySection {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #infoIcon, #activityIcon {
            font-size: 14px;
            color: #6B7280;
            margin-right: 8px;
        }

        #infoLabel, #activityText {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
        }

        #infoValue, #activityTime {
            font-size: 12px;
            color: #6B7280;
        }

        /* === ACTIONS RAPIDES === */
        #actionsSection {
            background: transparent;
        }

        #actionCard {
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #actionCard:hover {
            border-color: #3B82F6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        #actionIcon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        #actionTitle {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
        }
        """

    def get_dark_theme_styles(self):
        """Styles pour thème sombre"""
        return """
        /* === CONFIGURATION GLOBALE SOMBRE === */
        QMainWindow {
            background: #0F172A;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === SIDEBAR SOMBRE === */
        #dashboardSidebar {
            background: #1E293B;
            border: none;
        }

        #logoSection {
            background: transparent;
        }

        #logoIcon {
            font-size: 32px;
            color: #3B82F6;
            font-weight: bold;
        }

        #logoTitle {
            font-size: 20px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 5px 0;
        }

        #logoSubtitle {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
        }

        #themeControl {
            background: transparent;
        }

        #themeLabel {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
            margin-bottom: 5px;
        }

        #themeDropdown {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            color: #3B82F6;
            font-size: 12px;
            padding: 5px 10px;
        }

        #navigationMenu {
            background: transparent;
        }

        #navButton {
            background: transparent;
            border: none;
            border-radius: 8px;
            color: #94A3B8;
            text-align: left;
            padding: 8px;
        }

        #navButton:hover {
            background: #334155;
        }

        #navButton[active="true"] {
            background: rgba(59, 130, 246, 0.2);
            border-left: 3px solid #3B82F6;
            border-radius: 0 8px 8px 0;
        }

        #navIcon {
            font-size: 16px;
            color: #94A3B8;
        }

        #navButton[active="true"] #navIcon {
            color: #3B82F6;
        }

        #navTitle {
            font-size: 14px;
            font-weight: 500;
            color: #F8FAFC;
        }

        #adminSection {
            background: #334155;
            border-radius: 8px;
            margin: 10px;
        }

        #adminTitle {
            font-size: 12px;
            font-weight: 600;
            color: #F8FAFC;
            margin-bottom: 8px;
        }

        #profileButton {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            color: #3B82F6;
            font-size: 12px;
            padding: 8px;
        }

        #profileButton:hover {
            background: rgba(59, 130, 246, 0.2);
        }

        /* === CONTENU PRINCIPAL SOMBRE === */
        #mainContent {
            background: #0F172A;
        }

        #contentHeader {
            background: #1E293B;
            border-bottom: 1px solid #334155;
        }

        #welcomeMessage {
            font-size: 24px;
            font-weight: 700;
            color: #00BCD4;
        }

        #settingsButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #settingsButton:hover {
            background: #2563EB;
        }

        /* === CARTES KPI SOMBRES === */
        #kpiCard {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #kpiValue {
            font-size: 20px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 3px 0;
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
        }

        /* === SECTIONS SOMBRES === */
        #infoSection, #activitySection {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #infoIcon, #activityIcon {
            font-size: 14px;
            color: #94A3B8;
            margin-right: 8px;
        }

        #infoLabel, #activityText {
            font-size: 13px;
            font-weight: 500;
            color: #F8FAFC;
        }

        #infoValue, #activityTime {
            font-size: 12px;
            color: #94A3B8;
        }

        /* === ACTIONS RAPIDES SOMBRES === */
        #actionCard {
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #actionCard:hover {
            border-color: #3B82F6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }

        #actionTitle {
            font-size: 12px;
            font-weight: 600;
            color: #F8FAFC;
        }
        """

    # === MÉTHODES D'ÉVÉNEMENTS ===

    def change_theme_from_dropdown(self, theme_text):
        """Change le thème depuis le dropdown"""
        # Pour l'instant, on garde le thème système
        self.logger.info(f"Thème sélectionné: {theme_text}")

    def on_theme_changed(self, theme_name):
        """Réagit au changement de thème"""
        self.apply_styles()

    def switch_module(self, module_id):
        """Change de module"""
        self.logger.info(f"Module changé: {module_id}")
        # TODO: Implémenter la navigation entre modules

    def open_profile(self):
        """Ouvre le profil utilisateur"""
        self.logger.info("Ouverture du profil utilisateur")
        # TODO: Implémenter l'ouverture du profil

    def open_settings(self):
        """Ouvre les paramètres"""
        self.logger.info("Ouverture des paramètres")
        # TODO: Implémenter l'ouverture des paramètres

    def execute_action(self, action_id):
        """Exécute une action rapide"""
        self.logger.info(f"Action exécutée: {action_id}")
        # TODO: Implémenter les actions rapides

    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
