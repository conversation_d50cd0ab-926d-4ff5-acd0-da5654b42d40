#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Clients Moderne GSCOM
Gestion moderne des clients avec style cohérent au dashboard
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.styles.theme_manager import theme_manager
from src.bll.clients_service import ClientsService

class ModernClientsInterface(QMainWindow):
    """Interface clients moderne avec style dashboard"""
    
    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        self.clients_service = ClientsService()
        
        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Gestion des Clients")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        self.setup_ui()
        self.apply_styles()
        self.load_data()
        
        # Connecter aux changements de thème
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header moderne
        self.create_header(main_layout)
        
        # Zone de contenu
        self.create_content_area(main_layout)
    
    def create_header(self, layout):
        """Crée l'en-tête moderne"""
        header = QFrame()
        header.setObjectName("clientsHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(20)
        
        # Titre et description
        title_section = QVBoxLayout()
        
        title = QLabel("👥 Gestion des Clients")
        title.setObjectName("pageTitle")
        title_section.addWidget(title)
        
        subtitle = QLabel("Base de données clients et relations commerciales")
        subtitle.setObjectName("pageSubtitle")
        title_section.addWidget(subtitle)
        
        header_layout.addLayout(title_section)
        header_layout.addStretch()
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setObjectName("searchInput")
        self.search_input.setPlaceholderText("🔍 Rechercher un client...")
        self.search_input.setFixedWidth(300)
        self.search_input.textChanged.connect(self.search_clients)
        search_layout.addWidget(self.search_input)
        
        header_layout.addLayout(search_layout)
        
        # Actions rapides
        actions_layout = QHBoxLayout()
        
        # Bouton Nouveau Client
        new_client_btn = QPushButton("👤 Nouveau Client")
        new_client_btn.setObjectName("primaryButton")
        new_client_btn.clicked.connect(self.new_client)
        actions_layout.addWidget(new_client_btn)
        
        # Bouton Import
        import_btn = QPushButton("📥 Importer")
        import_btn.setObjectName("secondaryButton")
        import_btn.clicked.connect(self.import_clients)
        actions_layout.addWidget(import_btn)
        
        # Bouton Export
        export_btn = QPushButton("📤 Exporter")
        export_btn.setObjectName("secondaryButton")
        export_btn.clicked.connect(self.export_clients)
        actions_layout.addWidget(export_btn)
        
        header_layout.addLayout(actions_layout)
        layout.addWidget(header)
    
    def create_content_area(self, layout):
        """Crée la zone de contenu principal"""
        content = QFrame()
        content.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(30, 20, 30, 30)
        content_layout.setSpacing(25)
        
        # KPI clients
        self.create_clients_kpis(content_layout)
        
        # Sections principales
        self.create_main_sections(content_layout)
        
        layout.addWidget(content)
    
    def create_clients_kpis(self, layout):
        """Crée les KPI clients"""
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiSection")
        
        kpi_layout = QHBoxLayout(kpi_frame)
        kpi_layout.setContentsMargins(0, 0, 0, 0)
        kpi_layout.setSpacing(20)
        
        # Données KPI
        kpi_data = [
            ("👥", "Total Clients", "156", "Actifs", "#3B82F6"),
            ("🆕", "Nouveaux", "12", "Ce mois", "#10B981"),
            ("💰", "CA Moyen", "8.5k DA", "Par client", "#F59E0B"),
            ("📈", "Croissance", "+18%", "Mensuelle", "#8B5CF6"),
            ("⭐", "Satisfaction", "4.8/5", "Note moyenne", "#EF4444")
        ]
        
        for icon, title, value, subtitle, color in kpi_data:
            card = self.create_kpi_card(icon, title, value, subtitle, color)
            kpi_layout.addWidget(card)
        
        layout.addWidget(kpi_frame)
    
    def create_kpi_card(self, icon, title, value, subtitle, color):
        """Crée une carte KPI"""
        card = QFrame()
        card.setObjectName("kpiCard")
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(8)
        
        # Header avec icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("kpiIcon")
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        card_layout.addLayout(header_layout)
        
        # Valeur principale
        value_label = QLabel(value)
        value_label.setObjectName("kpiValue")
        card_layout.addWidget(value_label)
        
        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("kpiSubtitle")
        card_layout.addWidget(subtitle_label)
        
        return card
    
    def create_main_sections(self, layout):
        """Crée les sections principales"""
        sections_frame = QFrame()
        sections_layout = QHBoxLayout(sections_frame)
        sections_layout.setContentsMargins(0, 0, 0, 0)
        sections_layout.setSpacing(25)
        
        # Section Liste des Clients
        clients_list = self.create_clients_list_section()
        sections_layout.addWidget(clients_list, 2)  # 2/3 de l'espace
        
        # Section Détails Client
        client_details = self.create_client_details_section()
        sections_layout.addWidget(client_details, 1)  # 1/3 de l'espace
        
        layout.addWidget(sections_frame)
    
    def create_clients_list_section(self):
        """Crée la section liste des clients"""
        section = QFrame()
        section.setObjectName("clientsListSection")
        
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(12)
        
        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("📋")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("Liste des Clients")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # Filtres
        filter_combo = QComboBox()
        filter_combo.setObjectName("filterCombo")
        filter_combo.addItems(["Tous", "Actifs", "Inactifs", "VIP"])
        title_layout.addWidget(filter_combo)
        
        section_layout.addLayout(title_layout)
        
        # Tableau des clients
        self.clients_table = QTableWidget()
        self.clients_table.setObjectName("clientsTable")
        self.clients_table.setColumnCount(6)
        self.clients_table.setHorizontalHeaderLabels([
            "Nom", "Email", "Téléphone", "Ville", "CA Total", "Statut"
        ])
        
        # Données d'exemple
        self.populate_clients_table()
        
        # Configuration du tableau
        header = self.clients_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        self.clients_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.itemSelectionChanged.connect(self.on_client_selected)
        
        section_layout.addWidget(self.clients_table)
        
        return section
    
    def create_client_details_section(self):
        """Crée la section détails client"""
        section = QFrame()
        section.setObjectName("clientDetailsSection")
        
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(20, 15, 20, 15)
        section_layout.setSpacing(12)
        
        # Titre
        title_layout = QHBoxLayout()
        title_icon = QLabel("👤")
        title_icon.setObjectName("sectionIcon")
        title_layout.addWidget(title_icon)
        
        title_label = QLabel("Détails Client")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        section_layout.addLayout(title_layout)
        
        # Zone de détails (sera remplie lors de la sélection)
        self.details_widget = QWidget()
        details_layout = QVBoxLayout(self.details_widget)
        
        # Message par défaut
        default_msg = QLabel("Sélectionnez un client pour voir les détails")
        default_msg.setObjectName("defaultMessage")
        default_msg.setAlignment(Qt.AlignCenter)
        details_layout.addWidget(default_msg)
        
        section_layout.addWidget(self.details_widget)
        
        return section
    
    def populate_clients_table(self):
        """Remplit le tableau avec des données d'exemple"""
        clients_data = [
            ("Société ABC", "<EMAIL>", "0555-123-456", "Alger", "125,000 DA", "Actif"),
            ("Entreprise XYZ", "<EMAIL>", "0556-789-012", "Oran", "89,500 DA", "Actif"),
            ("Client DEF", "<EMAIL>", "0557-345-678", "Constantine", "156,200 DA", "VIP"),
            ("Société GHI", "<EMAIL>", "0558-901-234", "Annaba", "45,800 DA", "Inactif"),
            ("Entreprise JKL", "<EMAIL>", "0559-567-890", "Sétif", "78,900 DA", "Actif")
        ]
        
        self.clients_table.setRowCount(len(clients_data))
        
        for row, (name, email, phone, city, revenue, status) in enumerate(clients_data):
            self.clients_table.setItem(row, 0, QTableWidgetItem(name))
            self.clients_table.setItem(row, 1, QTableWidgetItem(email))
            self.clients_table.setItem(row, 2, QTableWidgetItem(phone))
            self.clients_table.setItem(row, 3, QTableWidgetItem(city))
            self.clients_table.setItem(row, 4, QTableWidgetItem(revenue))
            
            # Statut avec couleur
            status_item = QTableWidgetItem(status)
            if status == "Actif":
                status_item.setBackground(QColor("#10B981"))
                status_item.setForeground(QColor("white"))
            elif status == "VIP":
                status_item.setBackground(QColor("#F59E0B"))
                status_item.setForeground(QColor("white"))
            else:
                status_item.setBackground(QColor("#6B7280"))
                status_item.setForeground(QColor("white"))
            
            self.clients_table.setItem(row, 5, status_item)

    # === MÉTHODES D'ÉVÉNEMENTS ===

    def search_clients(self, text):
        """Recherche dans la liste des clients"""
        for row in range(self.clients_table.rowCount()):
            match = False
            for col in range(self.clients_table.columnCount()):
                item = self.clients_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    match = True
                    break
            self.clients_table.setRowHidden(row, not match)

    def on_client_selected(self):
        """Gère la sélection d'un client"""
        current_row = self.clients_table.currentRow()
        if current_row >= 0:
            client_name = self.clients_table.item(current_row, 0).text()
            self.show_client_details(client_name)

    def show_client_details(self, client_name):
        """Affiche les détails d'un client"""
        # Effacer le contenu précédent
        for i in reversed(range(self.details_widget.layout().count())):
            self.details_widget.layout().itemAt(i).widget().setParent(None)

        # Créer les détails
        details_layout = self.details_widget.layout()

        # Nom du client
        name_label = QLabel(client_name)
        name_label.setObjectName("clientName")
        details_layout.addWidget(name_label)

        # Informations détaillées
        info_data = [
            ("📧", "Email", "<EMAIL>"),
            ("📞", "Téléphone", "0555-123-456"),
            ("📍", "Adresse", "123 Rue de la Paix, Alger"),
            ("💰", "CA Total", "125,000 DA"),
            ("📅", "Client depuis", "15/03/2023"),
            ("🛒", "Commandes", "8 commandes"),
            ("📊", "Statut", "Actif")
        ]

        for icon, label, value in info_data:
            info_layout = QHBoxLayout()

            info_icon = QLabel(icon)
            info_icon.setObjectName("infoIcon")
            info_layout.addWidget(info_icon)

            info_label = QLabel(f"{label}:")
            info_label.setObjectName("infoLabel")
            info_layout.addWidget(info_label)

            info_layout.addStretch()

            info_value = QLabel(value)
            info_value.setObjectName("infoValue")
            info_layout.addWidget(info_value)

            details_layout.addLayout(info_layout)

        # Boutons d'action
        actions_layout = QHBoxLayout()

        edit_btn = QPushButton("✏️ Modifier")
        edit_btn.setObjectName("actionButton")
        edit_btn.clicked.connect(lambda: self.edit_client(client_name))
        actions_layout.addWidget(edit_btn)

        history_btn = QPushButton("📋 Historique")
        history_btn.setObjectName("actionButton")
        history_btn.clicked.connect(lambda: self.show_client_history(client_name))
        actions_layout.addWidget(history_btn)

        details_layout.addLayout(actions_layout)
        details_layout.addStretch()

    # === MÉTHODES D'ACTIONS ===

    def load_data(self):
        """Charge les données clients"""
        try:
            # Charger les statistiques clients
            self.clients_stats = self.clients_service.get_clients_stats()
            self.update_kpis()

        except Exception as e:
            self.logger.error(f"Erreur chargement données: {e}")

    def update_kpis(self):
        """Met à jour les KPI avec les vraies données"""
        if hasattr(self, 'clients_stats'):
            # TODO: Mettre à jour les cartes KPI avec les vraies données
            pass

    def new_client(self):
        """Crée un nouveau client"""
        self.logger.info("Création nouveau client")
        # TODO: Ouvrir l'interface de création de client

    def import_clients(self):
        """Importe des clients"""
        self.logger.info("Import clients")
        # TODO: Ouvrir l'interface d'import

    def export_clients(self):
        """Exporte les clients"""
        self.logger.info("Export clients")
        # TODO: Ouvrir l'interface d'export

    def edit_client(self, client_name):
        """Modifie un client"""
        self.logger.info(f"Modification client: {client_name}")
        # TODO: Ouvrir l'interface de modification

    def show_client_history(self, client_name):
        """Affiche l'historique d'un client"""
        self.logger.info(f"Historique client: {client_name}")
        # TODO: Ouvrir l'interface d'historique

    # === MÉTHODES DE STYLE ===

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet(self.get_clients_styles())

    def get_clients_styles(self):
        """Retourne les styles CSS selon le thème"""
        try:
            current_theme = theme_manager.current_theme
        except:
            current_theme = "light"

        if current_theme == "dark":
            return self.get_dark_theme_styles()
        else:
            return self.get_light_theme_styles()

    def on_theme_changed(self, theme_name):
        """Réagit au changement de thème"""
        self.apply_styles()

    def get_light_theme_styles(self):
        """Styles pour thème clair - Cohérent avec le dashboard"""
        return """
        /* === CONFIGURATION GLOBALE === */
        QMainWindow {
            background: #F8FAFC;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === HEADER CLIENTS === */
        #clientsHeader {
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }

        #pageSubtitle {
            font-size: 14px;
            color: #6B7280;
            margin-top: 2px;
        }

        #searchInput {
            background: white;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
        }

        #searchInput:focus {
            border-color: #3B82F6;
            outline: none;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        #secondaryButton {
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            color: #374151;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #secondaryButton:hover {
            background: #E5E7EB;
        }

        /* === CONTENU PRINCIPAL === */
        #contentArea {
            background: #F8FAFC;
        }

        /* === CARTES KPI === */
        #kpiSection {
            background: transparent;
        }

        #kpiCard {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #kpiIcon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #6B7280;
        }

        #kpiValue {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
            margin: 5px 0;
        }

        #kpiSubtitle {
            font-size: 11px;
            color: #9CA3AF;
        }

        /* === SECTIONS === */
        #clientsListSection, #clientDetailsSection {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #filterCombo {
            background: white;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            padding: 5px 10px;
            font-size: 12px;
        }

        /* === TABLEAU CLIENTS === */
        #clientsTable {
            background: white;
            border: none;
            gridline-color: #E5E7EB;
            font-size: 13px;
            color: #374151;
        }

        #clientsTable::item {
            padding: 8px;
            border-bottom: 1px solid #F3F4F6;
        }

        #clientsTable::item:selected {
            background: #EBF8FF;
            color: #1E40AF;
        }

        #clientsTable::item:alternate {
            background: #F9FAFB;
        }

        QHeaderView::section {
            background: #F9FAFB;
            border: none;
            border-bottom: 1px solid #E5E7EB;
            padding: 10px;
            font-weight: 600;
            color: #374151;
        }

        /* === DÉTAILS CLIENT === */
        #clientName {
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 15px;
        }

        #infoIcon {
            font-size: 14px;
            color: #6B7280;
            margin-right: 8px;
        }

        #infoLabel {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
        }

        #infoValue {
            font-size: 13px;
            color: #6B7280;
        }

        #actionButton {
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            color: #374151;
            font-size: 12px;
            font-weight: 500;
            padding: 8px 12px;
            margin: 2px;
        }

        #actionButton:hover {
            background: #E5E7EB;
        }

        #defaultMessage {
            font-size: 14px;
            color: #9CA3AF;
            font-style: italic;
        }
        """

    def get_dark_theme_styles(self):
        """Styles pour thème sombre"""
        return """
        /* === CONFIGURATION GLOBALE SOMBRE === */
        QMainWindow {
            background: #0F172A;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === HEADER SOMBRE === */
        #clientsHeader {
            background: #1E293B;
            border-bottom: 1px solid #334155;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #F8FAFC;
        }

        #pageSubtitle {
            font-size: 14px;
            color: #94A3B8;
            margin-top: 2px;
        }

        #searchInput {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            color: #F8FAFC;
        }

        #searchInput:focus {
            border-color: #3B82F6;
            outline: none;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        #secondaryButton {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 8px;
            color: #F8FAFC;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            margin: 0 5px;
        }

        #secondaryButton:hover {
            background: #475569;
        }

        /* === CONTENU SOMBRE === */
        #contentArea {
            background: #0F172A;
        }

        /* === CARTES KPI SOMBRES === */
        #kpiCard {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
        }

        #kpiTitle {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
        }

        #kpiValue {
            font-size: 24px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 5px 0;
        }

        #kpiSubtitle {
            font-size: 11px;
            color: #64748B;
        }

        /* === SECTIONS SOMBRES === */
        #clientsListSection, #clientDetailsSection {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
        }

        #sectionIcon {
            font-size: 18px;
            color: #00BCD4;
            margin-right: 8px;
        }

        #sectionTitle {
            font-size: 16px;
            font-weight: 600;
            color: #00BCD4;
        }

        #filterCombo {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 6px;
            padding: 5px 10px;
            font-size: 12px;
            color: #F8FAFC;
        }

        /* === TABLEAU SOMBRE === */
        #clientsTable {
            background: #1E293B;
            border: none;
            gridline-color: #334155;
            font-size: 13px;
            color: #F8FAFC;
        }

        #clientsTable::item {
            padding: 8px;
            border-bottom: 1px solid #334155;
        }

        #clientsTable::item:selected {
            background: #1E40AF;
            color: white;
        }

        #clientsTable::item:alternate {
            background: #0F172A;
        }

        QHeaderView::section {
            background: #0F172A;
            border: none;
            border-bottom: 1px solid #334155;
            padding: 10px;
            font-weight: 600;
            color: #F8FAFC;
        }

        /* === DÉTAILS SOMBRES === */
        #clientName {
            font-size: 18px;
            font-weight: 700;
            color: #F8FAFC;
            margin-bottom: 15px;
        }

        #infoIcon {
            font-size: 14px;
            color: #94A3B8;
            margin-right: 8px;
        }

        #infoLabel {
            font-size: 13px;
            font-weight: 500;
            color: #F8FAFC;
        }

        #infoValue {
            font-size: 13px;
            color: #94A3B8;
        }

        #actionButton {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #F8FAFC;
            font-size: 12px;
            font-weight: 500;
            padding: 8px 12px;
            margin: 2px;
        }

        #actionButton:hover {
            background: #475569;
        }

        #defaultMessage {
            font-size: 14px;
            color: #64748B;
            font-style: italic;
        }
        """
