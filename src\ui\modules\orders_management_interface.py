#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Gestion des Commandes GSCOM
Reproduction exacte de l'interface moderne de la capture d'écran
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.styles.theme_manager import theme_manager

class OrdersManagementInterface(QMainWindow):
    """Interface de gestion des commandes moderne"""
    
    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        
        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Gestion des Commandes")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        self.setup_ui()
        self.apply_styles()
        self.center_window()
        
        # Connecter aux changements de thème
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar gauche
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # Zone de contenu principal
        self.create_main_content()
        main_layout.addWidget(self.main_content)
    
    def create_sidebar(self):
        """Crée la sidebar bleue avec navigation"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("ordersSidebar")
        self.sidebar.setFixedWidth(250)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo et titre GSCOM
        self.create_logo_section(layout)
        
        # Contrôles de thème
        self.create_theme_controls(layout)
        
        # Navigation
        self.create_navigation_menu(layout)
        
        # Section administrateur en bas
        self.create_admin_section(layout)
    
    def create_logo_section(self, layout):
        """Crée la section logo GSCOM"""
        logo_frame = QFrame()
        logo_frame.setObjectName("logoSection")
        
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(20, 30, 20, 20)
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # Icône GSCOM
        logo_icon = QLabel("🏢")
        logo_icon.setObjectName("logoIcon")
        logo_icon.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_icon)
        
        # Titre GSCOM
        logo_title = QLabel("GSCOM")
        logo_title.setObjectName("logoTitle")
        logo_title.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_title)
        
        # Sous-titre
        logo_subtitle = QLabel("Gestion Commerciale")
        logo_subtitle.setObjectName("logoSubtitle")
        logo_subtitle.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_subtitle)
        
        layout.addWidget(logo_frame)
    
    def create_theme_controls(self, layout):
        """Crée les contrôles de thème"""
        theme_frame = QFrame()
        theme_frame.setObjectName("themeControls")
        
        theme_layout = QHBoxLayout(theme_frame)
        theme_layout.setContentsMargins(20, 10, 20, 10)
        theme_layout.setSpacing(5)
        
        # Boutons de thème
        themes = [
            ("🌙", "dark", "Sombre"),
            ("☀️", "light", "Clair"), 
            ("🖥️", "system", "Système")
        ]
        
        for icon, theme_name, tooltip in themes:
            btn = QPushButton(icon)
            btn.setObjectName("themeButton")
            btn.setFixedSize(30, 30)
            btn.setToolTip(tooltip)
            btn.clicked.connect(lambda checked, t=theme_name: self.change_theme(t))
            theme_layout.addWidget(btn)
        
        layout.addWidget(theme_frame)
    
    def create_navigation_menu(self, layout):
        """Crée le menu de navigation"""
        nav_frame = QFrame()
        nav_frame.setObjectName("navigationMenu")
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 20, 10, 20)
        nav_layout.setSpacing(5)
        
        # Modules de navigation
        nav_items = [
            ("📊", "Tableau de bord", "dashboard", False),
            ("💼", "Commercial", "commercial", False),
            ("📝", "Devis", "quotes", False),
            ("📋", "Commandes", "orders", True),  # Actif
            ("🧾", "Factures", "invoices", False),
            ("👥", "Clients", "clients", False),
            ("🏭", "Fournisseurs", "suppliers", False),
            ("📦", "Produits", "products", False),
            ("📊", "Stock", "stock", False),
            ("💰", "Comptabilité", "accounting", False),
            ("📄", "Rapports", "reports", False)
        ]
        
        for icon, title, module_id, is_active in nav_items:
            btn = self.create_nav_button(icon, title, module_id, is_active)
            nav_layout.addWidget(btn)
        
        layout.addWidget(nav_frame)
        layout.addStretch()
    
    def create_nav_button(self, icon, title, module_id, is_active):
        """Crée un bouton de navigation"""
        button = QPushButton()
        button.setObjectName("navButton")
        if is_active:
            button.setProperty("active", True)
        button.setFixedHeight(45)
        button.clicked.connect(lambda: self.switch_module(module_id))
        
        btn_layout = QHBoxLayout(button)
        btn_layout.setContentsMargins(15, 8, 15, 8)
        btn_layout.setSpacing(12)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        btn_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        btn_layout.addWidget(title_label)
        
        btn_layout.addStretch()
        
        return button
    
    def create_admin_section(self, layout):
        """Crée la section administrateur en bas"""
        admin_frame = QFrame()
        admin_frame.setObjectName("adminSection")
        
        admin_layout = QHBoxLayout(admin_frame)
        admin_layout.setContentsMargins(15, 15, 15, 20)
        admin_layout.setSpacing(10)
        
        # Icône admin
        admin_icon = QLabel("👤")
        admin_icon.setObjectName("adminIcon")
        admin_layout.addWidget(admin_icon)
        
        # Infos admin
        admin_info = QVBoxLayout()
        admin_info.setSpacing(2)
        
        admin_title = QLabel("Administrateur")
        admin_title.setObjectName("adminTitle")
        admin_info.addWidget(admin_title)
        
        admin_name = QLabel("Admin")
        admin_name.setObjectName("adminName")
        admin_info.addWidget(admin_name)
        
        admin_layout.addLayout(admin_info)
        admin_layout.addStretch()
        
        layout.addWidget(admin_frame)
    
    def create_main_content(self):
        """Crée la zone de contenu principal"""
        self.main_content = QFrame()
        self.main_content.setObjectName("mainContent")
        
        layout = QVBoxLayout(self.main_content)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Header avec titre
        self.create_header(layout)
        
        # Zone de contenu
        self.create_content_area(layout)
    
    def create_header(self, layout):
        """Crée l'en-tête avec titre et boutons"""
        header = QFrame()
        header.setObjectName("contentHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(20)
        
        # Titre
        title = QLabel("Gestion des Commandes")
        title.setObjectName("pageTitle")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Boutons d'action
        nouveau_btn = QPushButton("+ Nouveau")
        nouveau_btn.setObjectName("primaryButton")
        nouveau_btn.clicked.connect(self.new_order)
        header_layout.addWidget(nouveau_btn)
        
        export_btn = QPushButton("📤 Exporter")
        export_btn.setObjectName("secondaryButton")
        export_btn.clicked.connect(self.export_orders)
        header_layout.addWidget(export_btn)
        
        layout.addWidget(header)
    
    def create_content_area(self, layout):
        """Crée la zone de contenu avec KPI et graphiques"""
        content = QFrame()
        content.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(30, 20, 30, 30)
        content_layout.setSpacing(25)
        
        # Cartes KPI
        self.create_kpi_cards(content_layout)
        
        # Zone graphiques et tableau
        self.create_charts_and_table(content_layout)
        
        # Actions rapides
        self.create_quick_actions(content_layout)
        
        layout.addWidget(content)
    
    def create_kpi_cards(self, layout):
        """Crée les 4 cartes KPI colorées"""
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiSection")
        
        kpi_layout = QHBoxLayout(kpi_frame)
        kpi_layout.setContentsMargins(0, 0, 0, 0)
        kpi_layout.setSpacing(20)
        
        # Données KPI
        kpi_data = [
            ("16", "Clients actifs", "+12%", "#10B981", "👥"),  # Vert
            ("275", "Produits en stock", "+5%", "#3B82F6", "📦"),  # Bleu
            ("125374", "CA du mois", "+18%", "#F59E0B", "💰"),  # Orange
            ("28", "Commandes en cours", "-3%", "#1E40AF", "📋")  # Bleu foncé
        ]
        
        for value, title, change, color, icon in kpi_data:
            card = self.create_kpi_card(value, title, change, color, icon)
            kpi_layout.addWidget(card)
        
        layout.addWidget(kpi_frame)
    
    def create_kpi_card(self, value, title, change, color, icon):
        """Crée une carte KPI"""
        card = QFrame()
        card.setObjectName("kpiCard")
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(8)
        
        # En-tête avec icône
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("kpiIcon")
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        # Changement
        change_label = QLabel(change)
        change_label.setObjectName("kpiChange")
        if change.startswith("+"):
            change_label.setStyleSheet("color: #10B981; font-weight: 600;")
        else:
            change_label.setStyleSheet("color: #EF4444; font-weight: 600;")
        header_layout.addWidget(change_label)
        
        card_layout.addLayout(header_layout)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("kpiValue")
        card_layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        card_layout.addWidget(title_label)
        
        return card

    def create_charts_and_table(self, layout):
        """Crée la zone graphiques et tableau"""
        charts_frame = QFrame()
        charts_frame.setObjectName("chartsSection")

        charts_layout = QHBoxLayout(charts_frame)
        charts_layout.setContentsMargins(0, 0, 0, 0)
        charts_layout.setSpacing(20)

        # Zone graphique gauche
        self.create_chart_area(charts_layout)

        # Tableau des commandes droite
        self.create_orders_table(charts_layout)

        layout.addWidget(charts_frame)

    def create_chart_area(self, layout):
        """Crée la zone graphique"""
        chart_frame = QFrame()
        chart_frame.setObjectName("chartArea")
        chart_frame.setMinimumWidth(500)

        chart_layout = QVBoxLayout(chart_frame)
        chart_layout.setContentsMargins(25, 20, 25, 20)
        chart_layout.setSpacing(15)

        # Titre
        title = QLabel("Évolution du chiffre d'affaires")
        title.setObjectName("chartTitle")
        chart_layout.addWidget(title)

        # Zone graphique placeholder
        chart_placeholder = QFrame()
        chart_placeholder.setObjectName("chartPlaceholder")
        chart_placeholder.setMinimumHeight(250)

        placeholder_layout = QVBoxLayout(chart_placeholder)
        placeholder_layout.setAlignment(Qt.AlignCenter)

        # Icône graphique
        chart_icon = QLabel("📈")
        chart_icon.setAlignment(Qt.AlignCenter)
        chart_icon.setStyleSheet("font-size: 48px; color: #6B7280; margin: 20px;")
        placeholder_layout.addWidget(chart_icon)

        # Texte
        chart_text = QLabel("Graphique des ventes")
        chart_text.setAlignment(Qt.AlignCenter)
        chart_text.setObjectName("chartPlaceholderText")
        placeholder_layout.addWidget(chart_text)

        chart_layout.addWidget(chart_placeholder)
        layout.addWidget(chart_frame)

    def create_orders_table(self, layout):
        """Crée le tableau des dernières commandes"""
        table_frame = QFrame()
        table_frame.setObjectName("ordersTableFrame")
        table_frame.setMinimumWidth(400)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(25, 20, 25, 20)
        table_layout.setSpacing(15)

        # En-tête avec titre et lien
        header_layout = QHBoxLayout()

        title = QLabel("Dernières commandes")
        title.setObjectName("tableTitle")
        header_layout.addWidget(title)

        header_layout.addStretch()

        voir_tout = QLabel('<a href="#" style="color: #3B82F6; text-decoration: none;">Voir tout</a>')
        voir_tout.setObjectName("viewAllLink")
        voir_tout.linkActivated.connect(self.view_all_orders)
        header_layout.addWidget(voir_tout)

        table_layout.addLayout(header_layout)

        # Tableau
        table = QTableWidget()
        table.setObjectName("ordersTable")
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["N° Commande", "Client", "Montant", "Statut"])

        # Données d'exemple
        orders_data = [
            ("CMD-001", "Entreprise ABC", "2,450 DA", "LIVRÉ", "#10B981"),
            ("CMD-002", "Société XYZ", "1,890 DA", "EN COURS", "#F59E0B"),
            ("CMD-003", "Client DEF", "3,200 DA", "PRÉPARATION", "#3B82F6")
        ]

        table.setRowCount(len(orders_data))

        for row, (cmd_num, client, amount, status, color) in enumerate(orders_data):
            # N° Commande
            table.setItem(row, 0, QTableWidgetItem(cmd_num))

            # Client
            table.setItem(row, 1, QTableWidgetItem(client))

            # Montant
            table.setItem(row, 2, QTableWidgetItem(amount))

            # Statut avec couleur
            status_item = QTableWidgetItem(status)
            status_item.setForeground(QColor(color))
            table.setItem(row, 3, status_item)

        # Configuration du tableau
        table.horizontalHeader().setStretchLastSection(True)
        table.verticalHeader().setVisible(False)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setAlternatingRowColors(True)
        table.setMaximumHeight(200)

        table_layout.addWidget(table)
        layout.addWidget(table_frame)

    def create_quick_actions(self, layout):
        """Crée les actions rapides"""
        actions_frame = QFrame()
        actions_frame.setObjectName("quickActionsFrame")

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(15)

        # Titre
        title = QLabel("Actions rapides")
        title.setObjectName("actionsTitle")
        actions_layout.addWidget(title)

        # Boutons d'actions
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        actions = [
            ("👤", "Nouveau client", self.new_client),
            ("📋", "Créer facture", self.create_invoice),
            ("📦", "Ajouter produit", self.add_product),
            ("📊", "Voir rapports", self.view_reports)
        ]

        for icon, text, callback in actions:
            btn = self.create_action_button(icon, text, callback)
            buttons_layout.addWidget(btn)

        actions_layout.addLayout(buttons_layout)
        layout.addWidget(actions_frame)

    def create_action_button(self, icon, text, callback):
        """Crée un bouton d'action rapide"""
        button = QPushButton()
        button.setObjectName("actionButton")
        button.setFixedSize(150, 80)
        button.clicked.connect(callback)

        btn_layout = QVBoxLayout(button)
        btn_layout.setAlignment(Qt.AlignCenter)
        btn_layout.setSpacing(8)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; color: #3B82F6;")
        btn_layout.addWidget(icon_label)

        # Texte
        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setObjectName("actionText")
        btn_layout.addWidget(text_label)

        return button

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet(self.get_orders_styles())

    def get_orders_styles(self):
        """Retourne les styles CSS pour l'interface commandes selon le thème"""
        # Obtenir le thème actuel
        try:
            from src.ui.styles.theme_manager import theme_manager
            current_theme = theme_manager.current_theme
        except:
            current_theme = "light"

        if current_theme == "dark":
            return self.get_dark_theme_styles()
        else:
            # Thèmes 'light' et 'system' utilisent le style de la capture d'écran
            return self.get_light_theme_styles()

    def get_light_theme_styles(self):
        """Styles pour thème clair - Reproduction exacte de la capture d'écran"""
        return """
        /* === CONFIGURATION GLOBALE === */
        QMainWindow {
            background: #F8FAFC;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === SIDEBAR BLEUE (Exactement comme la capture d'écran) === */
        #ordersSidebar {
            background: #1E40AF;
            border: none;
        }

        #logoSection {
            background: transparent;
        }

        #logoIcon {
            font-size: 32px;
            color: white;
            font-weight: bold;
        }

        #logoTitle {
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin: 5px 0;
        }

        #logoSubtitle {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
        }

        #themeControls {
            background: transparent;
        }

        #themeButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 14px;
        }

        #themeButton:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        #navigationMenu {
            background: transparent;
        }

        #navButton {
            background: transparent;
            border: none;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            text-align: left;
            padding: 8px;
        }

        #navButton:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        #navButton[active="true"] {
            background: rgba(255, 255, 255, 0.15);
            border-left: 3px solid white;
            border-radius: 0 8px 8px 0;
        }

        #navIcon {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
        }

        #navTitle {
            font-size: 14px;
            font-weight: 500;
            color: white;
        }

        #adminSection {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin: 10px;
        }

        #adminIcon {
            font-size: 20px;
            color: white;
        }

        #adminTitle {
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        #adminName {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
        }

        /* === CONTENU PRINCIPAL (Comme la capture d'écran) === */
        #mainContent {
            background: #F8FAFC;
        }

        #contentHeader {
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        #secondaryButton {
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            color: #374151;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #secondaryButton:hover {
            background: #E5E7EB;
        }

        /* === CARTES KPI === */
        #kpiCard {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #kpiValue {
            font-size: 28px;
            font-weight: 700;
            color: #1F2937;
            margin: 5px 0;
        }

        #kpiTitle {
            font-size: 14px;
            font-weight: 500;
            color: #6B7280;
        }

        #kpiChange {
            font-size: 12px;
            font-weight: 600;
        }

        /* === GRAPHIQUES === */
        #chartArea, #ordersTableFrame {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #chartTitle, #tableTitle {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
        }

        #chartPlaceholder {
            background: #F9FAFB;
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
        }

        #chartPlaceholderText {
            font-size: 14px;
            color: #6B7280;
            font-weight: 500;
        }

        #viewAllLink {
            font-size: 14px;
            font-weight: 500;
        }

        /* === TABLEAU === */
        #ordersTable {
            background: white;
            border: none;
            gridline-color: #F3F4F6;
            font-size: 13px;
        }

        #ordersTable::item {
            padding: 8px;
            border-bottom: 1px solid #F3F4F6;
        }

        #ordersTable::item:selected {
            background: #EBF4FF;
        }

        QHeaderView::section {
            background: #F9FAFB;
            border: none;
            border-bottom: 1px solid #E5E7EB;
            padding: 10px;
            font-weight: 600;
            color: #374151;
        }

        /* === ACTIONS RAPIDES === */
        #actionsTitle {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 10px;
        }

        #actionButton {
            background: white;
            border: 2px solid #E5E7EB;
            border-radius: 12px;
        }

        #actionButton:hover {
            border-color: #3B82F6;
            background: #F8FAFC;
        }

        #actionText {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
        }
        """

    def get_dark_theme_styles(self):
        """Styles pour thème sombre"""
        return """
        /* === CONFIGURATION GLOBALE SOMBRE === */
        QMainWindow {
            background: #0F172A;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        }

        /* === SIDEBAR SOMBRE === */
        #ordersSidebar {
            background: #1E293B;
            border: none;
        }

        #logoSection {
            background: transparent;
        }

        #logoIcon {
            font-size: 32px;
            color: #3B82F6;
            font-weight: bold;
        }

        #logoTitle {
            font-size: 20px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 5px 0;
        }

        #logoSubtitle {
            font-size: 12px;
            font-weight: 500;
            color: #94A3B8;
        }

        #themeControls {
            background: transparent;
        }

        #themeButton {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            color: #3B82F6;
            font-size: 14px;
        }

        #themeButton:hover {
            background: rgba(59, 130, 246, 0.2);
        }

        #navigationMenu {
            background: transparent;
        }

        #navButton {
            background: transparent;
            border: none;
            border-radius: 8px;
            color: #94A3B8;
            text-align: left;
            padding: 8px;
        }

        #navButton:hover {
            background: #334155;
        }

        #navButton[active="true"] {
            background: rgba(59, 130, 246, 0.2);
            border-left: 3px solid #3B82F6;
            border-radius: 0 8px 8px 0;
        }

        #navIcon {
            font-size: 16px;
            color: #94A3B8;
        }

        #navButton[active="true"] #navIcon {
            color: #3B82F6;
        }

        #navTitle {
            font-size: 14px;
            font-weight: 500;
            color: #F8FAFC;
        }

        #adminSection {
            background: #334155;
            border-radius: 8px;
            margin: 10px;
        }

        #adminIcon {
            font-size: 20px;
            color: #3B82F6;
        }

        #adminTitle {
            font-size: 12px;
            font-weight: 600;
            color: #F8FAFC;
        }

        #adminName {
            font-size: 11px;
            color: #94A3B8;
        }

        /* === CONTENU PRINCIPAL SOMBRE === */
        #mainContent {
            background: #0F172A;
        }

        #contentHeader {
            background: #1E293B;
            border-bottom: 1px solid #334155;
        }

        #pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: #F8FAFC;
        }

        #primaryButton {
            background: #3B82F6;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #primaryButton:hover {
            background: #2563EB;
        }

        #secondaryButton {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 8px;
            color: #F8FAFC;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
        }

        #secondaryButton:hover {
            background: #475569;
        }

        /* === CARTES KPI SOMBRES === */
        #kpiCard {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #kpiValue {
            font-size: 28px;
            font-weight: 700;
            color: #F8FAFC;
            margin: 5px 0;
        }

        #kpiTitle {
            font-size: 14px;
            font-weight: 500;
            color: #94A3B8;
        }

        #kpiChange {
            font-size: 12px;
            font-weight: 600;
        }

        /* === GRAPHIQUES SOMBRES === */
        #chartArea, #ordersTableFrame {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        #chartTitle, #tableTitle {
            font-size: 18px;
            font-weight: 600;
            color: #F8FAFC;
        }

        #chartPlaceholder {
            background: #0F172A;
            border: 2px dashed #475569;
            border-radius: 8px;
        }

        #chartPlaceholderText {
            font-size: 14px;
            color: #94A3B8;
            font-weight: 500;
        }

        #viewAllLink {
            font-size: 14px;
            font-weight: 500;
        }

        /* === TABLEAU SOMBRE === */
        #ordersTable {
            background: #1E293B;
            border: none;
            gridline-color: #334155;
            font-size: 13px;
            color: #F8FAFC;
        }

        #ordersTable::item {
            padding: 8px;
            border-bottom: 1px solid #334155;
            color: #F8FAFC;
        }

        #ordersTable::item:selected {
            background: #334155;
        }

        QHeaderView::section {
            background: #334155;
            border: none;
            border-bottom: 1px solid #475569;
            padding: 10px;
            font-weight: 600;
            color: #F8FAFC;
        }

        /* === ACTIONS RAPIDES SOMBRES === */
        #actionsTitle {
            font-size: 18px;
            font-weight: 600;
            color: #F8FAFC;
            margin-bottom: 10px;
        }

        #actionButton {
            background: #1E293B;
            border: 2px solid #334155;
            border-radius: 12px;
        }

        #actionButton:hover {
            border-color: #3B82F6;
            background: #334155;
        }

        #actionText {
            font-size: 12px;
            font-weight: 600;
            color: #F8FAFC;
        }
        """

    # === MÉTHODES D'ÉVÉNEMENTS ===

    def change_theme(self, theme_name):
        """Change le thème de l'application"""
        theme_manager.set_theme(theme_name)
        self.logger.info(f"Thème changé: {theme_name}")

    def on_theme_changed(self, theme_name):
        """Réagit au changement de thème"""
        self.apply_styles()

    def switch_module(self, module_id):
        """Change de module"""
        self.logger.info(f"Module changé: {module_id}")
        # TODO: Implémenter la navigation entre modules

    def new_order(self):
        """Crée une nouvelle commande"""
        self.logger.info("Nouvelle commande")
        # TODO: Ouvrir le formulaire de nouvelle commande

    def export_orders(self):
        """Exporte les commandes"""
        self.logger.info("Export des commandes")
        # TODO: Implémenter l'export

    def view_all_orders(self):
        """Affiche toutes les commandes"""
        self.logger.info("Voir toutes les commandes")
        # TODO: Ouvrir la liste complète

    def new_client(self):
        """Nouveau client"""
        self.logger.info("Nouveau client")

    def create_invoice(self):
        """Créer facture"""
        self.logger.info("Créer facture")

    def add_product(self):
        """Ajouter produit"""
        self.logger.info("Ajouter produit")

    def view_reports(self):
        """Voir rapports"""
        self.logger.info("Voir rapports")

    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
