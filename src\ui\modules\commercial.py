#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module commercial moderne pour GSCOM
Gestion des devis, commandes, livraisons et factures
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import BaseWidget

class CommercialWidget(QWidget):
    """Widget principal du module commercial"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)

        self.init_ui()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête du module
        header_layout = QHBoxLayout()

        title = QLabel("Module Commercial")
        title.setObjectName("moduleTitle")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Bouton nouveau document
        new_doc_button = QPushButton("📄 Nouveau Document")
        new_doc_button.setObjectName("primaryButton")
        new_doc_button.clicked.connect(self.show_new_document_menu)
        header_layout.addWidget(new_doc_button)

        layout.addLayout(header_layout)

        # Onglets pour les différents types de documents
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("commercialTabs")

        # Onglet Devis
        self.quotes_tab = QuotesTab()
        self.tab_widget.addTab(self.quotes_tab, "💰 Devis")

        # Onglet Commandes
        self.orders_tab = OrdersTab()
        self.tab_widget.addTab(self.orders_tab, "📋 Commandes")

        # Onglet Livraisons
        self.deliveries_tab = DeliveriesTab()
        self.tab_widget.addTab(self.deliveries_tab, "🚚 Livraisons")

        # Onglet Factures
        self.invoices_tab = InvoicesTab()
        self.tab_widget.addTab(self.invoices_tab, "🧾 Factures")

        layout.addWidget(self.tab_widget)

        # Statistiques rapides
        self.create_stats_section(layout)

        self.apply_styles()

    def create_stats_section(self, layout):
        """Crée la section des statistiques rapides"""
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(15, 15, 15, 15)
        stats_layout.setSpacing(20)

        # Statistiques
        stats_data = [
            ("Devis en cours", "12", "#00d4ff"),
            ("Commandes du jour", "8", "#00ff88"),
            ("Livraisons en attente", "5", "#ffaa00"),
            ("CA du mois", "125,450 DA", "#ff6b6b")
        ]

        for title, value, color in stats_data:
            stat_card = self.create_stat_card(title, value, color)
            stats_layout.addWidget(stat_card)

        layout.addWidget(stats_frame)

    def create_stat_card(self, title, value, color):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(80)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(5)

        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("statValue")
        value_label.setStyleSheet(f"color: {color}; font-size: 20px; font-weight: bold;")
        layout.addWidget(value_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        title_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 12px;")
        layout.addWidget(title_label)

        return card

    def show_new_document_menu(self):
        """Affiche le menu de création de nouveau document"""
        menu = QMenu(self)
        menu.setObjectName("newDocumentMenu")

        # Actions
        quote_action = menu.addAction("💰 Nouveau Devis")
        quote_action.triggered.connect(self.new_quote)

        order_action = menu.addAction("📋 Nouvelle Commande")
        order_action.triggered.connect(self.new_order)

        delivery_action = menu.addAction("🚚 Nouvelle Livraison")
        delivery_action.triggered.connect(self.new_delivery)

        invoice_action = menu.addAction("🧾 Nouvelle Facture")
        invoice_action.triggered.connect(self.new_invoice)

        # Afficher le menu
        button = self.sender()
        menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))

    def new_quote(self):
        """Crée un nouveau devis"""
        self.tab_widget.setCurrentIndex(0)
        self.quotes_tab.new_quote()

    def new_order(self):
        """Crée une nouvelle commande"""
        self.tab_widget.setCurrentIndex(1)
        self.orders_tab.new_order()

    def new_delivery(self):
        """Crée une nouvelle livraison"""
        self.tab_widget.setCurrentIndex(2)
        self.deliveries_tab.new_delivery()

    def new_invoice(self):
        """Crée une nouvelle facture"""
        self.tab_widget.setCurrentIndex(3)
        self.invoices_tab.new_invoice()

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet("""
            #moduleTitle {
                font-size: 24px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 10px;
            }

            #primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00d4ff, stop:1 #ff00ff);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                min-height: 35px;
            }

            #primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00b8e6, stop:1 #e600e6);
            }

            #commercialTabs {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }

            #commercialTabs::pane {
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.02);
            }

            #commercialTabs::tab-bar {
                alignment: left;
            }

            #commercialTabs QTabBar::tab {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 10px 20px;
                margin-right: 2px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 500;
            }

            #commercialTabs QTabBar::tab:selected {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
                color: #00d4ff;
            }

            #commercialTabs QTabBar::tab:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            #statsFrame {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }

            #statCard {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }

            #statCard:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(0, 212, 255, 0.3);
            }

            #newDocumentMenu {
                background: rgba(20, 20, 40, 0.95);
                border: 1px solid rgba(0, 212, 255, 0.3);
                border-radius: 8px;
                color: white;
            }

            #newDocumentMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
            }

            #newDocumentMenu::item:selected {
                background: rgba(0, 212, 255, 0.2);
            }
        """)


class QuotesTab(QWidget):
    """Onglet de gestion des devis"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface des devis"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Barre d'outils
        toolbar = QHBoxLayout()

        new_button = QPushButton("➕ Nouveau Devis")
        new_button.clicked.connect(self.new_quote)
        toolbar.addWidget(new_button)

        toolbar.addStretch()

        search_input = QLineEdit()
        search_input.setPlaceholderText("🔍 Rechercher un devis...")
        search_input.setFixedWidth(250)
        toolbar.addWidget(search_input)

        layout.addLayout(toolbar)

        # Tableau des devis
        self.quotes_table = QTableWidget()
        self.quotes_table.setColumnCount(6)
        self.quotes_table.setHorizontalHeaderLabels([
            "N° Devis", "Client", "Date", "Montant", "Statut", "Actions"
        ])

        # Données d'exemple
        self.load_sample_quotes()

        layout.addWidget(self.quotes_table)

    def load_sample_quotes(self):
        """Charge des devis d'exemple"""
        sample_data = [
            ("DEV-2024-001", "SARL ALPHA", "15/01/2024", "45,000 DA", "En attente"),
            ("DEV-2024-002", "SPA BETA", "16/01/2024", "78,500 DA", "Accepté"),
            ("DEV-2024-003", "EURL GAMMA", "17/01/2024", "32,000 DA", "Refusé"),
        ]

        self.quotes_table.setRowCount(len(sample_data))

        for row, (num, client, date, amount, status) in enumerate(sample_data):
            self.quotes_table.setItem(row, 0, QTableWidgetItem(num))
            self.quotes_table.setItem(row, 1, QTableWidgetItem(client))
            self.quotes_table.setItem(row, 2, QTableWidgetItem(date))
            self.quotes_table.setItem(row, 3, QTableWidgetItem(amount))

            # Statut avec couleur
            status_item = QTableWidgetItem(status)
            if status == "Accepté":
                status_item.setForeground(QColor("#00ff88"))
            elif status == "Refusé":
                status_item.setForeground(QColor("#ff6b6b"))
            else:
                status_item.setForeground(QColor("#ffaa00"))

            self.quotes_table.setItem(row, 4, status_item)

            # Boutons d'action
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 2, 5, 2)

            view_btn = QPushButton("👁️")
            view_btn.setFixedSize(30, 25)
            edit_btn = QPushButton("✏️")
            edit_btn.setFixedSize(30, 25)

            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            actions_layout.addStretch()

            self.quotes_table.setCellWidget(row, 5, actions_widget)

    def new_quote(self):
        """Crée un nouveau devis"""
        QMessageBox.information(self, "Nouveau Devis", "Fonctionnalité en cours de développement")

    def show_info(self, title, message):
        """Affiche un message d'information"""
        QMessageBox.information(self, title, message)


class OrdersTab(QWidget):
    """Onglet de gestion des commandes"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface des commandes"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        info_label = QLabel("📋 Gestion des Commandes")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 18px; color: #00d4ff; margin: 50px;")
        layout.addWidget(info_label)

        desc_label = QLabel("Accédez au module dédié pour une gestion complète des commandes")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); line-height: 1.5; margin-bottom: 20px;")
        layout.addWidget(desc_label)

        # Bouton pour accéder au module commandes
        open_orders_btn = QPushButton("📋 Ouvrir le Module Commandes")
        open_orders_btn.setObjectName("primaryButton")
        open_orders_btn.clicked.connect(self.open_orders_module)
        open_orders_btn.setFixedSize(250, 50)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(open_orders_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        layout.addStretch()

    def open_orders_module(self):
        """Ouvre le module commandes dédié"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("orders")

    def get_main_window(self):
        """Récupère la fenêtre principale"""
        widget = self
        while widget:
            if hasattr(widget, 'switch_module'):
                return widget
            widget = widget.parent()
        return None

    def new_order(self):
        """Crée une nouvelle commande"""
        self.open_orders_module()

    def show_info(self, title, message):
        """Affiche un message d'information"""
        QMessageBox.information(self, title, message)


class DeliveriesTab(QWidget):
    """Onglet de gestion des livraisons"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface des livraisons"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        info_label = QLabel("🚚 Gestion des Livraisons")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 18px; color: #00d4ff; margin: 50px;")
        layout.addWidget(info_label)

        desc_label = QLabel("Module en cours de développement.\nFonctionnalités prévues :\n• Bon de livraison\n• Suivi des livraisons\n• Gestion des transporteurs")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); line-height: 1.5;")
        layout.addWidget(desc_label)

    def new_delivery(self):
        """Crée une nouvelle livraison"""
        QMessageBox.information(self, "Nouvelle Livraison", "Fonctionnalité en cours de développement")

    def show_info(self, title, message):
        """Affiche un message d'information"""
        QMessageBox.information(self, title, message)


class InvoicesTab(QWidget):
    """Onglet de gestion des factures"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface des factures"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        info_label = QLabel("🧾 Gestion des Factures")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 18px; color: #00d4ff; margin: 30px;")
        layout.addWidget(info_label)

        desc_label = QLabel("Accédez au module dédié pour une gestion complète des factures\navec génération PDF, suivi des paiements et plus encore.")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); line-height: 1.5; margin-bottom: 20px;")
        layout.addWidget(desc_label)

        # Bouton pour accéder au module factures
        open_invoices_btn = QPushButton("📋 Ouvrir le Module Factures")
        open_invoices_btn.setObjectName("primaryButton")
        open_invoices_btn.clicked.connect(self.open_invoices_module)
        open_invoices_btn.setFixedSize(250, 50)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(open_invoices_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        layout.addStretch()

    def open_invoices_module(self):
        """Ouvre le module factures dédié"""
        # Trouver la fenêtre principale et changer de module
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("invoices")

    def get_main_window(self):
        """Récupère la fenêtre principale"""
        widget = self
        while widget:
            if hasattr(widget, 'switch_module'):
                return widget
            widget = widget.parent()
        return None

    def new_invoice(self):
        """Crée une nouvelle facture"""
        self.open_invoices_module()
