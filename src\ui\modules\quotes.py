#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de gestion des devis
Interface complète pour la création et gestion des devis clients
"""

import logging
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget, FormDialog
from src.dal.models.commercial import Quote, DocumentStatus, QuoteLine
from src.dal.models.client import Client
from src.dal.models.product import Product
from src.dal.database import db_manager

# Définir QuoteStatus comme alias de DocumentStatus pour les devis
class QuoteStatus:
    DRAFT = DocumentStatus.DRAFT
    SENT = DocumentStatus.SENT
    ACCEPTED = DocumentStatus.CONFIRMED
    REJECTED = DocumentStatus.CANCELLED
    EXPIRED = DocumentStatus.EXPIRED

class QuotesWidget(ModuleWidget):
    """Widget principal pour la gestion des devis"""

    def __init__(self, parent=None):
        super().__init__("Gestion des Devis", parent)
        self.quotes = []
        self.load_data()

        # Ajouter des boutons spécifiques aux devis
        self.add_quotes_buttons()

    def add_quotes_buttons(self):
        """Ajoute des boutons spécifiques à la gestion des devis"""
        # Trouver la toolbar dans l'interface
        toolbar = None
        for child in self.findChildren(QFrame):
            if child.objectName() == "moduleToolbar":
                toolbar = child
                break

        if not toolbar:
            return

        # Ajouter un séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        toolbar.layout().insertWidget(3, separator)

        # Bouton nouveau devis
        self.new_quote_button = QPushButton("📝 Nouveau Devis")
        self.new_quote_button.setObjectName("actionButton")
        self.new_quote_button.clicked.connect(self.new_quote)
        toolbar.layout().insertWidget(4, self.new_quote_button)

        # Bouton convertir en commande
        self.convert_button = QPushButton("🔄 Convertir")
        self.convert_button.setObjectName("actionButton")
        self.convert_button.setEnabled(False)
        self.convert_button.clicked.connect(self.convert_to_order)
        toolbar.layout().insertWidget(5, self.convert_button)

        # Bouton imprimer
        self.print_button = QPushButton("🖨️ Imprimer")
        self.print_button.setObjectName("actionButton")
        self.print_button.setEnabled(False)
        self.print_button.clicked.connect(self.print_quote)
        toolbar.layout().insertWidget(6, self.print_button)

        # Bouton envoyer par email
        self.email_button = QPushButton("📧 Envoyer")
        self.email_button.setObjectName("actionButton")
        self.email_button.setEnabled(False)
        self.email_button.clicked.connect(self.send_quote_email)
        toolbar.layout().insertWidget(7, self.email_button)

    def create_data_table(self):
        """Crée le tableau des devis"""
        super().create_data_table()

        # Configuration des colonnes
        headers = ["N° Devis", "Date", "Client", "Montant HT", "TVA", "Montant TTC", "Statut", "Validité"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)

        # Ajuster la largeur des colonnes
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Devis
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Client
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Montant HT
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # TVA
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Montant TTC
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Validité

        # Menu contextuel
        self.data_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.data_table.customContextMenuRequested.connect(self.show_context_menu)

        # Connexion pour activer/désactiver les boutons
        self.data_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def load_data(self):
        """Charge les données des devis"""
        try:
            with db_manager.get_session() as session:
                # Charger les devis avec leurs relations
                quotes = session.query(Quote).order_by(Quote.created_at.desc()).all()

                # Créer des copies détachées
                self.quotes = []
                for quote in quotes:
                    # Forcer le chargement des relations
                    _ = quote.client
                    _ = quote.lines
                    self.quotes.append(quote)

                # Détacher les objets de la session
                session.expunge_all()

                self.populate_table()
                self.update_count(len(self.quotes))
                self.show_message(f"{len(self.quotes)} devis chargés", "success")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des devis: {e}")
            self.show_message("Erreur lors du chargement", "error")

    def populate_table(self):
        """Remplit le tableau avec les données"""
        self.data_table.setRowCount(len(self.quotes))

        for row, quote in enumerate(self.quotes):
            # N° Devis
            self.data_table.setItem(row, 0, QTableWidgetItem(quote.number or ""))

            # Date
            date_str = quote.date.strftime("%d/%m/%Y") if quote.date else ""
            self.data_table.setItem(row, 1, QTableWidgetItem(date_str))

            # Client
            client_name = quote.client.name if quote.client else "Client supprimé"
            self.data_table.setItem(row, 2, QTableWidgetItem(client_name))

            # Montant HT
            amount_ht = float(quote.total_amount_ht) if quote.total_amount_ht else 0.0
            self.data_table.setItem(row, 3, QTableWidgetItem(f"{amount_ht:.2f} DA"))

            # TVA
            tax_amount = float(quote.tax_amount) if quote.tax_amount else 0.0
            self.data_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f} DA"))

            # Montant TTC
            amount_ttc = float(quote.total_amount_ttc) if quote.total_amount_ttc else 0.0
            self.data_table.setItem(row, 5, QTableWidgetItem(f"{amount_ttc:.2f} DA"))

            # Statut
            status_text = {
                DocumentStatus.DRAFT: "Brouillon",
                DocumentStatus.SENT: "Envoyé",
                DocumentStatus.CONFIRMED: "Confirmé",
                DocumentStatus.CANCELLED: "Annulé",
                DocumentStatus.EXPIRED: "Expiré"
            }.get(quote.status, "Inconnu")

            status_item = QTableWidgetItem(status_text)
            if quote.status == DocumentStatus.DRAFT:
                status_item.setForeground(QColor("#ffaa00"))
            elif quote.status == DocumentStatus.SENT:
                status_item.setForeground(QColor("#00d4ff"))
            elif quote.status == DocumentStatus.CONFIRMED:
                status_item.setForeground(QColor("#00ff88"))
            elif quote.status == DocumentStatus.CANCELLED:
                status_item.setForeground(QColor("#ff6b6b"))
            else:
                status_item.setForeground(QColor("#888888"))
            self.data_table.setItem(row, 6, status_item)

            # Validité
            if quote.valid_until:
                valid_until_str = quote.valid_until.strftime("%d/%m/%Y")
                valid_item = QTableWidgetItem(valid_until_str)

                # Colorer selon la validité
                if quote.valid_until < datetime.now().date():
                    valid_item.setForeground(QColor("#ff6b6b"))  # Expiré
                elif quote.valid_until < (datetime.now() + timedelta(days=7)).date():
                    valid_item.setForeground(QColor("#ffaa00"))  # Expire bientôt
                else:
                    valid_item.setForeground(QColor("#00ff88"))  # Valide
            else:
                valid_item = QTableWidgetItem("Non définie")
                valid_item.setForeground(QColor("#888888"))

            self.data_table.setItem(row, 7, valid_item)

    def get_item_from_row(self, row):
        """Récupère le devis depuis la ligne sélectionnée"""
        if 0 <= row < len(self.quotes):
            return self.quotes[row]
        return None

    def on_selection_changed(self):
        """Gère le changement de sélection"""
        super().on_selection_changed()

        # Activer/désactiver les boutons selon le statut
        if self.current_item:
            can_convert = self.current_item.status == QuoteStatus.ACCEPTED
            can_print = self.current_item.status != QuoteStatus.DRAFT
            can_email = self.current_item.status in [QuoteStatus.DRAFT, QuoteStatus.SENT]

            self.convert_button.setEnabled(can_convert)
            self.print_button.setEnabled(can_print)
            self.email_button.setEnabled(can_email)
        else:
            self.convert_button.setEnabled(False)
            self.print_button.setEnabled(False)
            self.email_button.setEnabled(False)

    def handle_action(self, action):
        """Gère les actions du module"""
        if action == "new":
            self.new_quote()
        elif action == "edit" and self.current_item:
            self.edit_quote(self.current_item)
        elif action == "delete" and self.current_item:
            self.delete_quote(self.current_item)

        super().handle_action(action)

    def new_quote(self):
        """Crée un nouveau devis"""
        dialog = QuoteDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            quote_data = dialog.get_data()
            if self.save_quote(quote_data):
                self.refresh_data()

    def edit_quote(self, quote):
        """Modifie un devis existant"""
        if quote.status in [QuoteStatus.ACCEPTED, QuoteStatus.REJECTED]:
            QMessageBox.information(
                self,
                "Devis finalisé",
                "Ce devis est finalisé et ne peut plus être modifié."
            )
            return

        dialog = QuoteDialog(self, quote)
        if dialog.exec_() == QDialog.Accepted:
            quote_data = dialog.get_data()
            if self.update_quote(quote.id, quote_data):
                self.refresh_data()

    def delete_quote(self, quote):
        """Supprime un devis"""
        if quote.status == QuoteStatus.ACCEPTED:
            QMessageBox.warning(
                self,
                "Suppression impossible",
                "Impossible de supprimer un devis accepté."
            )
            return

        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le devis '{quote.number}' ?\n\n"
            "Cette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with db_manager.get_session() as session:
                    quote_to_delete = session.query(Quote).filter(
                        Quote.id == quote.id
                    ).first()

                    if quote_to_delete:
                        session.delete(quote_to_delete)
                        session.commit()

                        self.show_message("Devis supprimé avec succès", "success")
                        self.refresh_data()
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression: {e}")
                self.show_message("Erreur lors de la suppression", "error")

    def convert_to_order(self):
        """Convertit le devis en commande"""
        if not self.current_item or self.current_item.status != QuoteStatus.ACCEPTED:
            QMessageBox.warning(
                self,
                "Conversion impossible",
                "Seuls les devis acceptés peuvent être convertis en commande."
            )
            return

        reply = QMessageBox.question(
            self,
            "Convertir en commande",
            f"Voulez-vous convertir le devis '{self.current_item.number}' en commande ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                from src.dal.models.commercial import Order, OrderLine

                with db_manager.get_session() as session:
                    # Récupérer le devis complet avec ses lignes
                    quote = session.query(Quote).filter(Quote.id == self.current_item.id).first()
                    if not quote:
                        self.show_message("Devis introuvable", "error")
                        return

                    # Forcer le chargement des relations
                    _ = quote.lines
                    _ = quote.client

                    # Générer un numéro de commande
                    order_count = session.query(Order).count()
                    order_number = f"CMD{datetime.now().strftime('%Y%m%d')}{order_count + 1:04d}"

                    # Créer la commande
                    order_data = {
                        'number': order_number,
                        'client_id': quote.client_id,
                        'date': datetime.now().date(),
                        'delivery_date': quote.valid_until,  # Date de livraison = validité du devis
                        'description': f"Commande générée depuis le devis {quote.number}",
                        'status': DocumentStatus.DRAFT,
                        'user_id': quote.user_id,
                        'total_amount_ht': quote.total_amount_ht,
                        'tax_amount': quote.tax_amount,
                        'total_amount_ttc': quote.total_amount_ttc
                    }

                    order = Order(**order_data)
                    session.add(order)
                    session.flush()  # Pour obtenir l'ID

                    # Créer les lignes de commande depuis les lignes de devis
                    for quote_line in quote.lines:
                        order_line_data = {
                            'order_id': order.id,
                            'product_id': quote_line.product_id,
                            'description': quote_line.description,
                            'quantity': quote_line.quantity,
                            'unit_price': quote_line.unit_price,
                            'discount_rate': quote_line.discount_rate,
                            'total': quote_line.total
                        }
                        order_line = OrderLine(**order_line_data)
                        session.add(order_line)

                    # Marquer le devis comme converti (optionnel)
                    quote.converted_to_order = True
                    quote.converted_at = datetime.now()

                    session.commit()

                    # Message de succès avec option d'ouvrir la commande
                    reply = QMessageBox.question(
                        self,
                        "Conversion réussie",
                        f"Le devis '{quote.number}' a été converti en commande '{order_number}' avec succès !\n\n"
                        "Voulez-vous ouvrir le module Commandes pour voir la nouvelle commande ?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )

                    if reply == QMessageBox.Yes:
                        # Ouvrir le module commandes
                        self.open_orders_module()

                    # Actualiser la liste des devis
                    self.refresh_data()

            except Exception as e:
                self.logger.error(f"Erreur lors de la conversion: {e}")
                self.show_message("Erreur lors de la conversion en commande", "error")

    def open_orders_module(self):
        """Ouvre le module des commandes"""
        try:
            # Récupérer la fenêtre principale
            main_window = self.window()
            if hasattr(main_window, 'show_orders'):
                main_window.show_orders()
            elif hasattr(main_window, 'navigation_bar'):
                # Simuler un clic sur le bouton Commandes
                for button in main_window.navigation_bar.findChildren(QPushButton):
                    if "Commandes" in button.text():
                        button.click()
                        break
        except Exception as e:
            self.logger.error(f"Erreur ouverture module commandes: {e}")

    def print_quote(self):
        """Imprime le devis en PDF"""
        if not self.current_item:
            return

        try:
            from src.core.pdf_generator import PDFGenerator

            # Récupérer le devis complet avec ses relations
            with db_manager.get_session() as session:
                quote = session.query(Quote).filter(Quote.id == self.current_item.id).first()
                if quote:
                    # Forcer le chargement des relations
                    _ = quote.client
                    _ = quote.lines
                    for line in quote.lines:
                        _ = line.product

                    # Générer le PDF
                    pdf_generator = PDFGenerator()
                    filepath = pdf_generator.generate_quote_pdf(quote)

                    if filepath:
                        # Ouvrir le fichier PDF
                        import subprocess
                        import platform

                        if platform.system() == 'Windows':
                            os.startfile(filepath)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.call(['open', filepath])
                        else:  # Linux
                            subprocess.call(['xdg-open', filepath])

                        self.show_message(f"PDF généré: {os.path.basename(filepath)}", "success")
                    else:
                        self.show_message("Erreur lors de la génération PDF", "error")
                else:
                    self.show_message("Devis introuvable", "error")

        except ImportError:
            QMessageBox.warning(
                self,
                "Module manquant",
                "Le module reportlab n'est pas installé.\n"
                "Installez-le avec: pip install reportlab"
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération PDF: {e}")
            self.show_message("Erreur lors de la génération PDF", "error")

    def send_quote_email(self):
        """Envoie le devis par email"""
        if not self.current_item:
            return

        # TODO: Implémenter l'envoi par email
        QMessageBox.information(
            self,
            "Envoi email",
            f"Envoi du devis '{self.current_item.number}' par email en cours de développement"
        )

    def save_quote(self, data):
        """Sauvegarde un nouveau devis"""
        try:
            with db_manager.get_session() as session:
                # Extraire les lignes des données
                lines_data = data.pop('lines', [])

                # Créer le devis
                quote = Quote(**data)
                session.add(quote)
                session.flush()  # Pour obtenir l'ID

                # Créer les lignes
                for line_data in lines_data:
                    line_data['quote_id'] = quote.id
                    quote_line = QuoteLine(**line_data)
                    session.add(quote_line)

                session.commit()
                self.show_message("Devis créé avec succès", "success")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            self.show_message("Erreur lors de la sauvegarde", "error")
            return False

    def update_quote(self, quote_id, data):
        """Met à jour un devis existant"""
        try:
            with db_manager.get_session() as session:
                quote = session.query(Quote).filter(
                    Quote.id == quote_id
                ).first()

                if quote:
                    for key, value in data.items():
                        setattr(quote, key, value)

                    session.commit()
                    self.show_message("Devis modifié avec succès", "success")
                    return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            self.show_message("Erreur lors de la modification", "error")
            return False

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        if self.data_table.itemAt(position):
            menu = QMenu(self)

            view_action = menu.addAction("👁️ Voir détails")
            view_action.triggered.connect(self.view_quote_details)

            if self.current_item and self.current_item.status not in [QuoteStatus.ACCEPTED, QuoteStatus.REJECTED]:
                menu.addSeparator()
                edit_action = menu.addAction("✏️ Modifier")
                edit_action.triggered.connect(lambda: self.handle_action("edit"))

                # Actions de statut
                if self.current_item.status == QuoteStatus.DRAFT:
                    send_action = menu.addAction("📤 Marquer comme envoyé")
                    send_action.triggered.connect(self.mark_as_sent)

                if self.current_item.status == QuoteStatus.SENT:
                    accept_action = menu.addAction("✅ Marquer comme accepté")
                    accept_action.triggered.connect(self.mark_as_accepted)

                    reject_action = menu.addAction("❌ Marquer comme refusé")
                    reject_action.triggered.connect(self.mark_as_rejected)

                menu.addSeparator()
                delete_action = menu.addAction("🗑️ Supprimer")
                delete_action.triggered.connect(lambda: self.handle_action("delete"))

            menu.exec_(self.data_table.mapToGlobal(position))

    def view_quote_details(self):
        """Affiche les détails du devis"""
        if self.current_item:
            dialog = QuoteDetailsDialog(self.current_item, self)
            dialog.exec_()

    def mark_as_sent(self):
        """Marque le devis comme envoyé"""
        if self.current_item:
            self.update_quote_status(self.current_item.id, QuoteStatus.SENT)

    def mark_as_accepted(self):
        """Marque le devis comme accepté"""
        if self.current_item:
            self.update_quote_status(self.current_item.id, QuoteStatus.ACCEPTED)

    def mark_as_rejected(self):
        """Marque le devis comme refusé"""
        if self.current_item:
            self.update_quote_status(self.current_item.id, QuoteStatus.REJECTED)

    def update_quote_status(self, quote_id, new_status):
        """Met à jour le statut d'un devis"""
        try:
            with db_manager.get_session() as session:
                quote = session.query(Quote).filter(Quote.id == quote_id).first()
                if quote:
                    quote.status = new_status
                    session.commit()
                    self.show_message("Statut mis à jour", "success")
                    self.refresh_data()
        except Exception as e:
            self.logger.error(f"Erreur mise à jour statut: {e}")
            self.show_message("Erreur lors de la mise à jour", "error")


class QuoteDialog(FormDialog):
    """Formulaire de création/modification de devis"""

    def __init__(self, parent=None, quote=None):
        self.quote = quote
        self.is_edit_mode = quote is not None

        title = "Modifier le devis" if self.is_edit_mode else "Nouveau devis"
        super().__init__(title, parent)
        self.setFixedSize(800, 700)
        self.setup_form()

        if self.is_edit_mode:
            self.load_quote_data()

    def setup_form(self):
        """Configure le formulaire"""
        # Client
        self.client_combo = QComboBox()
        self.load_clients()
        self.add_field("Client *:", self.client_combo)

        # Numéro de devis
        self.number_input = QLineEdit()
        self.number_input.setPlaceholderText("Numéro automatique si vide")
        self.add_field("N° Devis:", self.number_input)

        # Date
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setDisplayFormat("dd/MM/yyyy")
        self.add_field("Date:", self.date_input)

        # Validité
        self.valid_until_input = QDateEdit()
        self.valid_until_input.setDate(QDate.currentDate().addDays(30))
        self.valid_until_input.setDisplayFormat("dd/MM/yyyy")
        self.add_field("Valide jusqu'au:", self.valid_until_input)

        # Description
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("Description du devis...")
        self.add_field("Description:", self.description_input)

        # Gestion des lignes de devis
        self.setup_lines_section()

    def setup_lines_section(self):
        """Configure la section des lignes de devis"""
        # Créer un groupe pour les lignes
        lines_group = QGroupBox("Lignes du devis")
        lines_layout = QVBoxLayout(lines_group)

        # Toolbar pour les lignes
        lines_toolbar = QHBoxLayout()

        add_line_btn = QPushButton("➕ Ajouter ligne")
        add_line_btn.clicked.connect(self.add_quote_line)
        lines_toolbar.addWidget(add_line_btn)

        remove_line_btn = QPushButton("➖ Supprimer ligne")
        remove_line_btn.clicked.connect(self.remove_quote_line)
        lines_toolbar.addWidget(remove_line_btn)

        lines_toolbar.addStretch()
        lines_layout.addLayout(lines_toolbar)

        # Tableau des lignes
        self.lines_table = QTableWidget()
        self.lines_table.setColumnCount(6)
        self.lines_table.setHorizontalHeaderLabels([
            "Produit", "Description", "Quantité", "Prix Unit.", "Remise %", "Total"
        ])

        # Ajuster les colonnes
        header = self.lines_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        self.lines_table.setMaximumHeight(200)
        lines_layout.addWidget(self.lines_table)

        # Totaux
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.subtotal_label = QLabel("Sous-total HT: 0.00 DA")
        self.tax_label = QLabel("TVA (19%): 0.00 DA")
        self.total_label = QLabel("Total TTC: 0.00 DA")

        totals_widget = QWidget()
        totals_widget_layout = QVBoxLayout(totals_widget)
        totals_widget_layout.addWidget(self.subtotal_label)
        totals_widget_layout.addWidget(self.tax_label)
        totals_widget_layout.addWidget(self.total_label)

        totals_layout.addWidget(totals_widget)
        lines_layout.addLayout(totals_layout)

        # Ajouter le groupe au formulaire
        self.form_layout.addRow(lines_group)

        # Initialiser avec une ligne vide
        self.quote_lines = []
        self.add_quote_line()

    def add_quote_line(self):
        """Ajoute une nouvelle ligne de devis"""
        row = self.lines_table.rowCount()
        self.lines_table.insertRow(row)

        # Combo produit
        product_combo = QComboBox()
        self.load_products_for_combo(product_combo)
        product_combo.currentTextChanged.connect(self.on_product_changed)
        self.lines_table.setCellWidget(row, 0, product_combo)

        # Description
        description_input = QLineEdit()
        description_input.textChanged.connect(self.calculate_line_total)
        self.lines_table.setCellWidget(row, 1, description_input)

        # Quantité
        quantity_input = QDoubleSpinBox()
        quantity_input.setMinimum(0.01)
        quantity_input.setMaximum(999999.99)
        quantity_input.setValue(1.0)
        quantity_input.setDecimals(2)
        quantity_input.valueChanged.connect(self.calculate_line_total)
        self.lines_table.setCellWidget(row, 2, quantity_input)

        # Prix unitaire
        price_input = QDoubleSpinBox()
        price_input.setMinimum(0.0)
        price_input.setMaximum(999999.99)
        price_input.setDecimals(2)
        price_input.valueChanged.connect(self.calculate_line_total)
        self.lines_table.setCellWidget(row, 3, price_input)

        # Remise
        discount_input = QDoubleSpinBox()
        discount_input.setMinimum(0.0)
        discount_input.setMaximum(100.0)
        discount_input.setDecimals(1)
        discount_input.setSuffix(" %")
        discount_input.valueChanged.connect(self.calculate_line_total)
        self.lines_table.setCellWidget(row, 4, discount_input)

        # Total (lecture seule)
        total_item = QTableWidgetItem("0.00 DA")
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        self.lines_table.setItem(row, 5, total_item)

    def remove_quote_line(self):
        """Supprime la ligne sélectionnée"""
        current_row = self.lines_table.currentRow()
        if current_row >= 0 and self.lines_table.rowCount() > 1:
            self.lines_table.removeRow(current_row)
            self.calculate_totals()

    def load_products_for_combo(self, combo):
        """Charge les produits dans un combo"""
        try:
            with db_manager.get_session() as session:
                products = session.query(Product).filter(Product.is_active == True).all()

                combo.addItem("", None)  # Option vide
                for product in products:
                    combo.addItem(f"{product.name} - {product.price:.2f} DA", product.id)
        except Exception as e:
            print(f"Erreur chargement produits: {e}")

    def on_product_changed(self):
        """Gère le changement de produit"""
        sender = self.sender()
        if not isinstance(sender, QComboBox):
            return

        # Trouver la ligne correspondante
        for row in range(self.lines_table.rowCount()):
            if self.lines_table.cellWidget(row, 0) == sender:
                product_id = sender.currentData()
                if product_id:
                    # Charger les données du produit
                    try:
                        with db_manager.get_session() as session:
                            product = session.query(Product).filter(Product.id == product_id).first()
                            if product:
                                # Remplir la description et le prix
                                description_widget = self.lines_table.cellWidget(row, 1)
                                price_widget = self.lines_table.cellWidget(row, 3)

                                if description_widget:
                                    description_widget.setText(product.description or product.name)
                                if price_widget:
                                    price_widget.setValue(float(product.price or 0))

                                self.calculate_line_total()
                    except Exception as e:
                        print(f"Erreur chargement produit: {e}")
                break

    def calculate_line_total(self):
        """Calcule le total d'une ligne"""
        sender = self.sender()

        # Trouver la ligne correspondante
        for row in range(self.lines_table.rowCount()):
            widgets = [
                self.lines_table.cellWidget(row, 2),  # Quantité
                self.lines_table.cellWidget(row, 3),  # Prix
                self.lines_table.cellWidget(row, 4)   # Remise
            ]

            if sender in widgets:
                quantity_widget = self.lines_table.cellWidget(row, 2)
                price_widget = self.lines_table.cellWidget(row, 3)
                discount_widget = self.lines_table.cellWidget(row, 4)
                total_item = self.lines_table.item(row, 5)

                if all([quantity_widget, price_widget, discount_widget, total_item]):
                    quantity = quantity_widget.value()
                    price = price_widget.value()
                    discount = discount_widget.value()

                    # Calculer le total
                    subtotal = quantity * price
                    discount_amount = subtotal * (discount / 100)
                    total = subtotal - discount_amount

                    total_item.setText(f"{total:.2f} DA")

                self.calculate_totals()
                break

    def calculate_totals(self):
        """Calcule les totaux généraux"""
        subtotal = 0.0

        for row in range(self.lines_table.rowCount()):
            total_item = self.lines_table.item(row, 5)
            if total_item:
                try:
                    amount_text = total_item.text().replace(" DA", "")
                    subtotal += float(amount_text)
                except ValueError:
                    pass

        tax_rate = 0.19  # TVA 19%
        tax_amount = subtotal * tax_rate
        total_ttc = subtotal + tax_amount

        self.subtotal_label.setText(f"Sous-total HT: {subtotal:.2f} DA")
        self.tax_label.setText(f"TVA (19%): {tax_amount:.2f} DA")
        self.total_label.setText(f"Total TTC: {total_ttc:.2f} DA")

    def load_clients(self):
        """Charge la liste des clients"""
        try:
            with db_manager.get_session() as session:
                clients = session.query(Client).filter(Client.is_active == True).all()

                self.client_combo.addItem("Sélectionner un client", None)
                for client in clients:
                    self.client_combo.addItem(client.name, client.id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des clients: {e}")

    def load_quote_data(self):
        """Charge les données du devis à modifier"""
        if self.quote:
            # Client
            for i in range(self.client_combo.count()):
                if self.client_combo.itemData(i) == self.quote.client_id:
                    self.client_combo.setCurrentIndex(i)
                    break

            self.number_input.setText(self.quote.number or "")

            if self.quote.date:
                self.date_input.setDate(QDate.fromString(
                    self.quote.date.strftime("%d/%m/%Y"), "dd/MM/yyyy"
                ))

            if self.quote.valid_until:
                self.valid_until_input.setDate(QDate.fromString(
                    self.quote.valid_until.strftime("%d/%m/%Y"), "dd/MM/yyyy"
                ))

            self.description_input.setPlainText(self.quote.description or "")

    def get_data(self):
        """Récupère les données du formulaire"""
        from src.ui.main_window import MainWindow

        # Récupérer l'utilisateur actuel
        main_window = None
        widget = self.parent()
        while widget and not isinstance(widget, MainWindow):
            widget = widget.parent()

        if widget and hasattr(widget, 'current_user'):
            user_id = widget.current_user.id
        else:
            user_id = 1  # Fallback

        # Calculer les totaux
        subtotal = 0.0
        for row in range(self.lines_table.rowCount()):
            total_item = self.lines_table.item(row, 5)
            if total_item:
                try:
                    amount_text = total_item.text().replace(" DA", "")
                    subtotal += float(amount_text)
                except ValueError:
                    pass

        tax_rate = 0.19
        tax_amount = subtotal * tax_rate
        total_ttc = subtotal + tax_amount

        # Récupérer les lignes
        lines_data = []
        for row in range(self.lines_table.rowCount()):
            product_combo = self.lines_table.cellWidget(row, 0)
            description_widget = self.lines_table.cellWidget(row, 1)
            quantity_widget = self.lines_table.cellWidget(row, 2)
            price_widget = self.lines_table.cellWidget(row, 3)
            discount_widget = self.lines_table.cellWidget(row, 4)

            if all([product_combo, description_widget, quantity_widget, price_widget, discount_widget]):
                product_id = product_combo.currentData()
                description = description_widget.text().strip()
                quantity = quantity_widget.value()
                price = price_widget.value()
                discount = discount_widget.value()

                if quantity > 0 and price > 0:
                    line_subtotal = quantity * price
                    line_discount = line_subtotal * (discount / 100)
                    line_total = line_subtotal - line_discount

                    lines_data.append({
                        'product_id': product_id,
                        'description': description or None,
                        'quantity': quantity,
                        'unit_price': price,
                        'discount_rate': discount,
                        'total': line_total
                    })

        return {
            'client_id': self.client_combo.currentData(),
            'number': self.number_input.text().strip() or None,
            'date': self.date_input.date().toPyDate(),
            'valid_until': self.valid_until_input.date().toPyDate(),
            'description': self.description_input.toPlainText().strip() or None,
            'status': QuoteStatus.DRAFT,
            'user_id': user_id,
            'total_amount_ht': subtotal,
            'tax_amount': tax_amount,
            'total_amount_ttc': total_ttc,
            'lines': lines_data
        }


class QuoteDetailsDialog(QDialog):
    """Dialogue d'affichage des détails d'un devis"""

    def __init__(self, quote, parent=None):
        super().__init__(parent)
        self.quote = quote
        self.setWindowTitle(f"Devis - {quote.number}")
        self.setFixedSize(700, 500)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = QHBoxLayout()

        icon_label = QLabel("📝")
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)

        title_layout = QVBoxLayout()
        title_label = QLabel(f"Devis {self.quote.number}")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #c77dff;")
        title_layout.addWidget(title_label)

        status_text = {
            QuoteStatus.DRAFT: "Brouillon",
            QuoteStatus.SENT: "Envoyé",
            QuoteStatus.ACCEPTED: "Accepté",
            QuoteStatus.REJECTED: "Refusé",
            QuoteStatus.EXPIRED: "Expiré"
        }.get(self.quote.status, "Inconnu")

        subtitle_label = QLabel(f"Statut: {status_text}")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        title_layout.addWidget(subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Informations générales
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)

        info_data = [
            ("Client", self.quote.client.name if self.quote.client else "Client supprimé"),
            ("Date", self.quote.date.strftime("%d/%m/%Y") if self.quote.date else "Non définie"),
            ("Validité", self.quote.valid_until.strftime("%d/%m/%Y") if self.quote.valid_until else "Non définie"),
            ("Description", self.quote.description or "Aucune description")
        ]

        self.add_section(info_layout, "Informations générales", info_data)

        # Montants
        if hasattr(self.quote, 'total_amount_ht') and self.quote.total_amount_ht:
            amounts_data = [
                ("Montant HT", f"{float(self.quote.total_amount_ht):.2f} DA"),
                ("TVA", f"{float(self.quote.tax_amount or 0):.2f} DA"),
                ("Montant TTC", f"{float(self.quote.total_amount_ttc or 0):.2f} DA")
            ]
            self.add_section(info_layout, "Montants", amounts_data)

        layout.addWidget(info_widget)

        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def add_section(self, layout, title, items):
        """Ajoute une section d'informations"""
        # Titre de section
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #c77dff;
            margin-top: 15px;
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)

        # Contenu de la section
        for label, value in items:
            item_layout = QHBoxLayout()

            label_widget = QLabel(f"{label}:")
            label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-weight: 500;")
            label_widget.setFixedWidth(120)
            item_layout.addWidget(label_widget)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: white;")
            value_widget.setWordWrap(True)
            item_layout.addWidget(value_widget)

            layout.addLayout(item_layout)

    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
            }

            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 10px 20px;
                color: white;
                font-weight: 500;
            }

            QPushButton:hover {
                background: rgba(199, 125, 255, 0.2);
                border-color: #c77dff;
            }
        """)
