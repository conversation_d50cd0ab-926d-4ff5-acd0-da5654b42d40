// ===== GSCOM - Script Principal =====
// Gestion des thèmes, navigation et animations

class GSCOMApp {
    constructor() {
        this.currentTheme = 'dark';
        this.currentModule = 'dashboard';
        this.init();
    }

    init() {
        this.setupThemeSwitcher();
        this.setupNavigation();
        this.setupAnimations();
        this.setupResponsive();
        this.loadSavedTheme();
        
        console.log('🚀 GSCOM App initialisée');
    }

    // ===== GESTION DES THÈMES =====
    setupThemeSwitcher() {
        const themeButtons = document.querySelectorAll('.theme-btn');
        
        themeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const theme = button.dataset.theme;
                this.changeTheme(theme);
                this.updateThemeButtons(button);
            });
        });
    }

    changeTheme(themeName) {
        const body = document.body;
        
        // Supprimer les classes de thème existantes
        body.classList.remove('theme-dark', 'theme-light', 'theme-system');
        
        // Appliquer le nouveau thème
        if (themeName === 'system') {
            // Détecter le thème système
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
        } else {
            body.classList.add(`theme-${themeName}`);
        }
        
        this.currentTheme = themeName;
        this.saveTheme(themeName);
        
        // Animation de changement de thème
        this.animateThemeChange();
        
        console.log(`🎨 Thème changé vers: ${themeName}`);
    }

    updateThemeButtons(activeButton) {
        const themeButtons = document.querySelectorAll('.theme-btn');
        themeButtons.forEach(btn => btn.classList.remove('active'));
        activeButton.classList.add('active');
    }

    animateThemeChange() {
        const body = document.body;
        body.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        
        // Effet de pulsation
        body.style.transform = 'scale(0.98)';
        setTimeout(() => {
            body.style.transform = 'scale(1)';
        }, 150);
        
        setTimeout(() => {
            body.style.transition = '';
        }, 500);
    }

    saveTheme(theme) {
        localStorage.setItem('gscom-theme', theme);
    }

    loadSavedTheme() {
        const savedTheme = localStorage.getItem('gscom-theme') || 'dark';
        const themeButton = document.querySelector(`[data-theme="${savedTheme}"]`);
        
        if (themeButton) {
            this.changeTheme(savedTheme);
            this.updateThemeButtons(themeButton);
        }
    }

    // ===== NAVIGATION =====
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const module = item.dataset.module;
                this.navigateToModule(module, item);
            });
        });
    }

    navigateToModule(moduleName, navItem) {
        // Mettre à jour la navigation active
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        navItem.classList.add('active');
        
        // Mettre à jour le titre de la page
        this.updatePageTitle(moduleName);
        
        // Animation de changement de contenu
        this.animateContentChange();
        
        this.currentModule = moduleName;
        console.log(`📱 Navigation vers: ${moduleName}`);
    }

    updatePageTitle(moduleName) {
        const titles = {
            dashboard: 'Tableau de bord',
            commercial: 'Gestion Commerciale',
            quotes: 'Gestion des Devis',
            orders: 'Gestion des Commandes',
            invoices: 'Gestion des Factures',
            clients: 'Gestion des Clients',
            suppliers: 'Gestion des Fournisseurs',
            products: 'Gestion des Produits',
            stock: 'Gestion des Stocks',
            accounting: 'Comptabilité',
            reports: 'Rapports et Analyses',
            settings: 'Paramètres'
        };
        
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) {
            pageTitle.textContent = titles[moduleName] || 'GSCOM';
        }
    }

    animateContentChange() {
        const mainContent = document.querySelector('.main-content');
        
        mainContent.style.opacity = '0.7';
        mainContent.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
            mainContent.style.opacity = '1';
            mainContent.style.transform = 'translateY(0)';
        }, 200);
    }

    // ===== ANIMATIONS =====
    setupAnimations() {
        this.observeElements();
        this.setupHoverEffects();
        this.setupCounterAnimations();
    }

    observeElements() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, { threshold: 0.1 });

        // Observer les cartes et éléments
        document.querySelectorAll('.stat-card, .card, .action-btn').forEach(el => {
            observer.observe(el);
        });
    }

    setupHoverEffects() {
        // Effet de parallaxe sur les cartes statistiques
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Effet de glow sur les boutons primaires
        document.querySelectorAll('.btn-primary').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.boxShadow = '0 12px 40px rgba(0, 102, 204, 0.4)';
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.style.boxShadow = '0 4px 12px var(--shadow-medium)';
            });
        });
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('.stat-value');
        
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += step;
                if (current < target) {
                    counter.textContent = Math.floor(current).toLocaleString();
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target.toLocaleString();
                }
            };
            
            // Démarrer l'animation quand l'élément est visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }

    // ===== RESPONSIVE =====
    setupResponsive() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        // Bouton menu mobile (à ajouter si nécessaire)
        this.createMobileMenuButton();
        
        // Gestion du redimensionnement
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        this.handleResize();
    }

    createMobileMenuButton() {
        if (window.innerWidth <= 1024) {
            const mobileBtn = document.createElement('button');
            mobileBtn.className = 'mobile-menu-btn';
            mobileBtn.innerHTML = '<i class="fas fa-bars"></i>';
            mobileBtn.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: var(--primary);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px;
                cursor: pointer;
                display: none;
            `;
            
            if (window.innerWidth <= 1024) {
                mobileBtn.style.display = 'block';
            }
            
            mobileBtn.addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('open');
            });
            
            document.body.appendChild(mobileBtn);
        }
    }

    handleResize() {
        const sidebar = document.querySelector('.sidebar');
        const mobileBtn = document.querySelector('.mobile-menu-btn');
        
        if (window.innerWidth > 1024) {
            sidebar.classList.remove('open');
            if (mobileBtn) mobileBtn.style.display = 'none';
        } else {
            if (mobileBtn) mobileBtn.style.display = 'block';
        }
    }

    // ===== UTILITAIRES =====
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--${type === 'error' ? 'error' : 'primary'});
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // ===== DONNÉES SIMULÉES =====
    updateDashboardData() {
        // Simulation de mise à jour des données en temps réel
        const stats = [
            { selector: '.stat-value', values: ['152', '325', '127,890 DA', '31'] }
        ];
        
        // Mise à jour périodique (simulation)
        setInterval(() => {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach((stat, index) => {
                const randomChange = Math.floor(Math.random() * 10) - 5;
                const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
                const newValue = Math.max(0, currentValue + randomChange);
                
                if (index < 3) { // Pas pour le montant
                    stat.textContent = newValue.toString();
                }
            });
        }, 30000); // Toutes les 30 secondes
    }
}

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
    const app = new GSCOMApp();
    
    // Démarrer les mises à jour de données
    app.updateDashboardData();
    
    // Gestion du thème système
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (app.currentTheme === 'system') {
            app.changeTheme('system');
        }
    });
    
    console.log('✅ GSCOM App prête !');
});
