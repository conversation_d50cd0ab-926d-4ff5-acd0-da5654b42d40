#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des thèmes corrigés GSCOM
Teste spécifiquement les améliorations des thèmes clair et système
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Import du système de thèmes
from src.ui.styles.theme_manager import theme_manager
from src.ui.components.theme_switcher import ThemeSwitcher


class ThemeTestWidget(QWidget):
    """Widget de test pour visualiser les thèmes"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.connect_theme_manager()
        self.apply_theme()
    
    def init_ui(self):
        """Initialise l'interface de test"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Titre principal
        title = QLabel("🎨 Test des Thèmes GSCOM Corrigés")
        title.setObjectName("mainTitle")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Commutateur de thème
        theme_section = QGroupBox("Sélection du thème")
        theme_layout = QVBoxLayout(theme_section)
        
        self.theme_switcher = ThemeSwitcher()
        theme_layout.addWidget(self.theme_switcher)
        
        # Informations sur le thème actuel
        self.theme_info = QLabel()
        self.theme_info.setObjectName("themeInfo")
        self.theme_info.setWordWrap(True)
        theme_layout.addWidget(self.theme_info)
        
        layout.addWidget(theme_section)
        
        # Section de test des éléments UI
        ui_test_section = QGroupBox("Test des éléments d'interface")
        ui_layout = QGridLayout(ui_test_section)
        
        # Boutons de test
        primary_btn = QPushButton("Bouton Primaire")
        primary_btn.setProperty("class", "primary")
        ui_layout.addWidget(primary_btn, 0, 0)
        
        secondary_btn = QPushButton("Bouton Secondaire")
        secondary_btn.setProperty("class", "secondary")
        ui_layout.addWidget(secondary_btn, 0, 1)
        
        outline_btn = QPushButton("Bouton Outline")
        outline_btn.setProperty("class", "outline")
        ui_layout.addWidget(outline_btn, 0, 2)
        
        # Champs de saisie
        line_edit = QLineEdit("Champ de saisie")
        line_edit.setPlaceholderText("Tapez ici...")
        ui_layout.addWidget(line_edit, 1, 0, 1, 2)
        
        combo_box = QComboBox()
        combo_box.addItems(["Option 1", "Option 2", "Option 3"])
        ui_layout.addWidget(combo_box, 1, 2)
        
        # Zone de texte
        text_edit = QTextEdit()
        text_edit.setPlainText("Zone de texte multiligne\nPour tester la lisibilité\nDu thème sélectionné")
        text_edit.setMaximumHeight(80)
        ui_layout.addWidget(text_edit, 2, 0, 1, 3)
        
        layout.addWidget(ui_test_section)
        
        # Section de test des cartes
        cards_section = QGroupBox("Test des cartes et surfaces")
        cards_layout = QHBoxLayout(cards_section)
        
        # Carte normale
        card1 = QFrame()
        card1.setProperty("class", "card")
        card1_layout = QVBoxLayout(card1)
        card1_layout.addWidget(QLabel("Carte Standard"))
        card1_layout.addWidget(QLabel("Contenu de la carte avec du texte"))
        cards_layout.addWidget(card1)
        
        # Carte statistique
        card2 = QFrame()
        card2.setProperty("class", "stat-card")
        card2_layout = QVBoxLayout(card2)
        stat_value = QLabel("1,234")
        stat_value.setObjectName("statValue")
        stat_value.setAlignment(Qt.AlignCenter)
        card2_layout.addWidget(stat_value)
        card2_layout.addWidget(QLabel("Statistique"))
        cards_layout.addWidget(card2)
        
        # Carte élevée
        card3 = QFrame()
        card3.setProperty("class", "elevated-card")
        card3_layout = QVBoxLayout(card3)
        card3_layout.addWidget(QLabel("Carte Élevée"))
        card3_layout.addWidget(QLabel("Avec ombre prononcée"))
        cards_layout.addWidget(card3)
        
        layout.addWidget(cards_section)
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 Actualiser Thème Système")
        refresh_btn.clicked.connect(self.refresh_system_theme)
        actions_layout.addWidget(refresh_btn)
        
        info_btn = QPushButton("ℹ️ Infos Thème Système")
        info_btn.clicked.connect(self.show_system_info)
        actions_layout.addWidget(info_btn)
        
        test_btn = QPushButton("🧪 Test Contraste")
        test_btn.clicked.connect(self.test_contrast)
        actions_layout.addWidget(test_btn)
        
        layout.addLayout(actions_layout)
        
        self.update_theme_info()
    
    def connect_theme_manager(self):
        """Connecte le gestionnaire de thèmes"""
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def on_theme_changed(self, theme_name: str):
        """Réagit au changement de thème"""
        print(f"🎨 Thème changé vers: {theme_name}")
        self.apply_theme()
        self.update_theme_info()
    
    def apply_theme(self):
        """Applique le thème actuel"""
        colors = theme_manager.get_theme_colors()
        base_styles = theme_manager.get_theme_styles()
        
        # Styles spécifiques au widget de test
        test_styles = f"""
        QWidget {{
            background: {colors.get('background', '#ffffff')};
            color: {colors.get('text_primary', '#1e293b')};
            font-family: 'Segoe UI', sans-serif;
        }}
        
        #mainTitle {{
            font-size: 24px;
            font-weight: bold;
            color: {colors.get('primary', '#0066cc')};
            margin: 20px 0;
            padding: 16px;
            background: {colors.get('surface_variant', '#f1f5f9')};
            border-radius: 12px;
            border: 2px solid {colors.get('border', '#e2e8f0')};
        }}
        
        #themeInfo {{
            font-size: 14px;
            color: {colors.get('text_secondary', '#64748b')};
            background: {colors.get('surface', '#ffffff')};
            padding: 16px;
            border-radius: 8px;
            border: 1px solid {colors.get('border', '#e2e8f0')};
            margin: 8px 0;
        }}
        
        #statValue {{
            font-size: 32px;
            font-weight: bold;
            color: {colors.get('primary', '#0066cc')};
        }}
        
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {colors.get('border', '#e2e8f0')};
            border-radius: 12px;
            margin-top: 12px;
            padding-top: 12px;
            background: {colors.get('surface', '#ffffff')};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: {colors.get('primary', '#0066cc')};
            background: {colors.get('background', '#ffffff')};
        }}
        """
        
        # Importer les styles des composants
        from src.ui.styles.components import get_modern_components_styles
        component_styles = get_modern_components_styles(colors)
        
        complete_styles = base_styles + component_styles + test_styles
        self.setStyleSheet(complete_styles)
    
    def update_theme_info(self):
        """Met à jour les informations sur le thème"""
        current_theme = theme_manager.current_theme
        theme_obj = theme_manager.get_current_theme()
        is_dark = theme_obj.is_dark() if theme_obj else False
        colors = theme_manager.get_theme_colors()
        
        info_text = f"""
        <b>Thème actuel :</b> {current_theme.title()}<br>
        <b>Type :</b> {'Sombre' if is_dark else 'Clair'}<br>
        <b>Couleur primaire :</b> {colors.get('primary', 'N/A')}<br>
        <b>Couleur de fond :</b> {colors.get('background', 'N/A')}<br>
        <b>Couleur de texte :</b> {colors.get('text_primary', 'N/A')}<br>
        <b>Thèmes disponibles :</b> {', '.join(theme_manager.get_available_themes())}
        """
        
        if current_theme == "system":
            try:
                system_info = theme_manager.get_system_theme_info()
                if system_info:
                    info_text += "<br><br><b>Informations système :</b><br>"
                    for key, value in system_info.items():
                        info_text += f"• {key}: {value}<br>"
            except Exception as e:
                info_text += f"<br><br><b>Erreur système :</b> {e}"
        
        self.theme_info.setText(info_text)
    
    def refresh_system_theme(self):
        """Actualise le thème système"""
        try:
            if theme_manager.refresh_system_theme():
                print("✅ Thème système actualisé")
            else:
                print("ℹ️ Aucun changement détecté")
        except Exception as e:
            print(f"❌ Erreur lors de l'actualisation: {e}")
    
    def show_system_info(self):
        """Affiche les informations détaillées du thème système"""
        try:
            info = theme_manager.get_system_theme_info()
            if info:
                print("\n🖥️ Informations détaillées du thème système:")
                for key, value in info.items():
                    print(f"  {key}: {value}")
            else:
                print("❌ Aucune information système disponible")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    def test_contrast(self):
        """Teste le contraste des couleurs"""
        colors = theme_manager.get_theme_colors()
        
        print("\n🎨 Test de contraste des couleurs:")
        print(f"  Background: {colors.get('background', 'N/A')}")
        print(f"  Text Primary: {colors.get('text_primary', 'N/A')}")
        print(f"  Primary: {colors.get('primary', 'N/A')}")
        print(f"  Surface: {colors.get('surface', 'N/A')}")
        print(f"  Border: {colors.get('border', 'N/A')}")


class ThemeTestWindow(QMainWindow):
    """Fenêtre principale de test des thèmes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des Thèmes GSCOM - Version Corrigée")
        self.setGeometry(200, 200, 900, 700)
        
        # Widget central
        self.test_widget = ThemeTestWidget()
        self.setCentralWidget(self.test_widget)
        
        # Barre de statut
        self.statusBar().showMessage("Prêt - Testez les différents thèmes")


def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSCOM Theme Test - Fixed")
    app.setApplicationVersion("2.0")
    
    print("🚀 Test des thèmes GSCOM corrigés...")
    print(f"📋 Thèmes disponibles: {theme_manager.get_available_themes()}")
    print(f"🎨 Thème actuel: {theme_manager.current_theme}")
    
    # Créer et afficher la fenêtre de test
    window = ThemeTestWindow()
    window.show()
    
    print("✅ Fenêtre de test affichée")
    print("💡 Testez les thèmes clair et système pour voir les améliorations")
    print("🔧 Utilisez les boutons d'action pour tester les fonctionnalités")
    
    # Lancer l'application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
