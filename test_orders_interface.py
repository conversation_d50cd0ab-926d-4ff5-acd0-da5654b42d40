#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'Interface Gestion des Commandes GSCOM
Reproduction exacte de l'interface de la capture d'écran
"""

import sys
import os
import logging
from datetime import datetime

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MockUser:
    """Utilisateur fictif pour les tests"""
    def __init__(self):
        self.first_name = "Administrateur"
        self.username = "admin"
        self.id = 1

class OrdersTestApp(QApplication):
    """Application de test pour l'interface gestion des commandes"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setApplicationName("GSCOM Orders Management Test")
        self.setApplicationVersion("1.0")
        
        # Utilisateur fictif
        self.current_user = MockUser()
        
        self.setup_test_environment()
        self.create_orders_interface()
    
    def setup_test_environment(self):
        """Configure l'environnement de test"""
        print("🚀 Configuration de l'environnement de test...")
        
        # Vérifier les dépendances
        try:
            from src.ui.styles.theme_manager import theme_manager
            print("✅ Theme Manager disponible")
        except ImportError as e:
            print(f"⚠️ Theme Manager non disponible: {e}")
            self.create_mock_theme_manager()
        
        try:
            from src.ui.modules.orders_management_interface import OrdersManagementInterface
            print("✅ Interface Gestion Commandes disponible")
        except ImportError as e:
            print(f"❌ Interface non disponible: {e}")
            return False
        
        return True
    
    def create_mock_theme_manager(self):
        """Crée un gestionnaire de thème fictif"""
        class MockThemeManager(QObject):
            theme_changed = pyqtSignal(str)
            
            def set_theme(self, theme_name):
                print(f"Mock: Thème changé vers {theme_name}")
                self.theme_changed.emit(theme_name)
        
        # Créer le module fictif
        import types
        theme_module = types.ModuleType('theme_manager')
        theme_module.theme_manager = MockThemeManager()
        
        # L'ajouter aux modules système
        sys.modules['src.ui.styles.theme_manager'] = theme_module
    
    def create_orders_interface(self):
        """Crée l'interface gestion des commandes"""
        try:
            from src.ui.modules.orders_management_interface import OrdersManagementInterface
            
            self.orders_interface = OrdersManagementInterface(self.current_user)
            self.orders_interface.show()
            
            print("✅ Interface Gestion des Commandes créée avec succès")
            
            # Afficher les informations de test
            self.show_test_info()
            
        except Exception as e:
            print(f"❌ Erreur création interface: {e}")
            import traceback
            traceback.print_exc()
            self.create_fallback_interface()
    
    def create_fallback_interface(self):
        """Crée une interface de fallback en cas d'erreur"""
        print("🔄 Création d'une interface de fallback...")
        
        self.fallback_window = QMainWindow()
        self.fallback_window.setWindowTitle("GSCOM - Test Interface Commandes (Fallback)")
        self.fallback_window.setGeometry(200, 200, 1200, 800)
        
        central_widget = QWidget()
        self.fallback_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(20)
        
        # Icône d'erreur
        error_icon = QLabel("⚠️")
        error_icon.setAlignment(Qt.AlignCenter)
        error_icon.setStyleSheet("font-size: 64px; color: #EF4444; margin: 20px;")
        layout.addWidget(error_icon)
        
        # Titre d'erreur
        error_title = QLabel("Interface Gestion Commandes Non Disponible")
        error_title.setAlignment(Qt.AlignCenter)
        error_title.setStyleSheet("""
            font-size: 24px; 
            font-weight: bold; 
            color: #1F2937; 
            margin: 10px;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        """)
        layout.addWidget(error_title)
        
        # Message d'information
        info_message = QLabel(
            "L'interface de gestion des commandes est en cours de développement.\n\n"
            "Design reproduit selon la capture d'écran :\n"
            "• Sidebar bleue (#1E40AF) avec navigation\n"
            "• Header blanc avec titre et boutons d'action\n"
            "• 4 Cartes KPI colorées (vert, bleu, orange, bleu foncé)\n"
            "• Zone graphique centrale avec placeholder\n"
            "• Tableau des dernières commandes\n"
            "• Actions rapides avec icônes\n"
            "• Design moderne et professionnel\n"
            "• Responsive et accessible"
        )
        info_message.setAlignment(Qt.AlignCenter)
        info_message.setWordWrap(True)
        info_message.setStyleSheet("""
            font-size: 14px; 
            color: #6B7280; 
            line-height: 1.6;
            background: #F9FAFB;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            max-width: 600px;
            font-family: 'Inter', 'Segoe UI', sans-serif;
        """)
        layout.addWidget(info_message)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        retry_btn = QPushButton("🔄 Réessayer")
        retry_btn.setStyleSheet("""
            QPushButton {
                background: #3B82F6;
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                padding: 12px 24px;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
            QPushButton:hover {
                background: #2563EB;
            }
        """)
        retry_btn.clicked.connect(self.retry_interface_creation)
        buttons_layout.addWidget(retry_btn)
        
        close_btn = QPushButton("❌ Fermer")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #F3F4F6;
                border: 2px solid #E5E7EB;
                border-radius: 8px;
                color: #6B7280;
                font-size: 14px;
                font-weight: 600;
                padding: 12px 24px;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
            QPushButton:hover {
                background: #E5E7EB;
                color: #374151;
            }
        """)
        close_btn.clicked.connect(self.quit)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
        # Appliquer le style global
        self.fallback_window.setStyleSheet("""
            QMainWindow {
                background: #F8FAFC;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
        """)
        
        self.fallback_window.show()
    
    def retry_interface_creation(self):
        """Réessaie de créer l'interface"""
        if hasattr(self, 'fallback_window'):
            self.fallback_window.close()
        self.create_orders_interface()
    
    def show_test_info(self):
        """Affiche les informations de test"""
        print("\n" + "="*70)
        print("🎨 INTERFACE GESTION DES COMMANDES - TEST")
        print("="*70)
        print(f"📅 Démarré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
        print(f"👤 Utilisateur: {self.current_user.first_name}")
        print(f"🖥️ Résolution: {self.primaryScreen().size().width()}x{self.primaryScreen().size().height()}")
        print("\n📋 REPRODUCTION EXACTE DE LA CAPTURE D'ÉCRAN:")
        print("  ✅ Sidebar bleue (#1E40AF) avec logo GSCOM")
        print("  ✅ Navigation avec 11 modules (Commandes actif)")
        print("  ✅ Contrôles de thème (🌙 ☀️ 🖥️)")
        print("  ✅ Section administrateur en bas")
        print("  ✅ Header blanc avec titre 'Gestion des Commandes'")
        print("  ✅ Boutons 'Nouveau' et 'Exporter'")
        print("  ✅ 4 Cartes KPI colorées:")
        print("      • 16 Clients actifs (+12%) - Vert")
        print("      • 275 Produits en stock (+5%) - Bleu")
        print("      • 125374 CA du mois (+18%) - Orange")
        print("      • 28 Commandes en cours (-3%) - Bleu foncé")
        print("  ✅ Zone graphique 'Évolution du chiffre d'affaires'")
        print("  ✅ Tableau 'Dernières commandes' avec lien 'Voir tout'")
        print("  ✅ Actions rapides (4 boutons avec icônes)")
        print("\n🎨 DESIGN SPECIFICATIONS:")
        print("  • Sidebar: #1E40AF (Bleu professionnel)")
        print("  • Background: #F8FAFC (Gris très clair)")
        print("  • Cards: Blanc avec bordures #E5E7EB")
        print("  • Typographie: Inter/Segoe UI")
        print("  • Bordures: 12px radius, ombres légères")
        print("  • KPI Colors: Vert #10B981, Bleu #3B82F6, Orange #F59E0B")
        print("\n🧪 INSTRUCTIONS DE TEST:")
        print("  1. Vérifiez la correspondance avec la capture d'écran")
        print("  2. Testez la navigation entre modules")
        print("  3. Utilisez les boutons de thème")
        print("  4. Cliquez sur 'Nouveau' et 'Exporter'")
        print("  5. Testez le lien 'Voir tout' du tableau")
        print("  6. Cliquez sur les actions rapides")
        print("  7. Vérifiez la responsivité")
        print("="*70)

def main():
    """Fonction principale"""
    print("🚀 Démarrage du test Interface Gestion des Commandes GSCOM...")
    
    # Créer l'application
    app = OrdersTestApp(sys.argv)
    
    print("✅ Application de test créée")
    print("🎨 Interface moderne en cours de chargement...")
    
    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
