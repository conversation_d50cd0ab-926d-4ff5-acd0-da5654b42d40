#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de gestion des codes-barres
Interface pour générer, imprimer et scanner des codes-barres
"""

import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget
from src.dal.database import db_manager
from src.dal.models.product import Product

# Vérifier la disponibilité des bibliothèques de codes-barres
try:
    import barcode
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class BarcodeManagerWidget(ModuleWidget):
    """Widget principal pour la gestion des codes-barres"""

    def __init__(self, parent=None):
        super().__init__("Gestion des Codes-barres", parent)
        self.setup_barcode_ui()

    def setup_barcode_ui(self):
        """Configure l'interface spécifique aux codes-barres"""
        # Remplacer le tableau par une interface de codes-barres
        self.content_layout.removeWidget(self.data_table)
        self.data_table.hide()
        self.data_table.deleteLater()

        # Créer l'interface de codes-barres
        self.create_barcode_interface()

    def create_barcode_interface(self):
        """Crée l'interface de codes-barres"""
        # Layout principal avec onglets
        self.tabs = QTabWidget()
        self.tabs.setObjectName("barcodeTabs")

        # Appliquer des styles pour améliorer la visibilité
        try:
            from src.ui.styles.module_styles import get_module_styles, get_barcode_specific_styles
            combined_styles = get_module_styles() + get_barcode_specific_styles()
            self.tabs.setStyleSheet(combined_styles)
        except ImportError:
            # Fallback styles si le module de styles n'est pas disponible
            self.tabs.setStyleSheet("""
                QTabWidget::pane {
                    border: 2px solid rgba(0, 212, 255, 0.3);
                    border-radius: 12px;
                    background: rgba(255, 255, 255, 0.08);
                    margin-top: 8px;
                    padding: 10px;
                }

                QTabBar::tab {
                    background: rgba(255, 255, 255, 0.15);
                    border: 1px solid rgba(255, 255, 255, 0.25);
                    border-bottom: none;
                    border-radius: 10px 10px 0 0;
                    padding: 12px 20px;
                    margin-right: 3px;
                    color: rgba(255, 255, 255, 0.85);
                    font-weight: 600;
                    min-width: 100px;
                }

                QTabBar::tab:selected {
                    background: rgba(0, 212, 255, 0.4);
                    border-color: #00d4ff;
                    color: #ffffff;
                    font-weight: bold;
                    border-bottom: 2px solid #00d4ff;
                }

                QTabBar::tab:hover:!selected {
                    background: rgba(255, 255, 255, 0.2);
                    color: #ffffff;
                    border-color: rgba(255, 255, 255, 0.4);
                }
            """)

        # Onglet Génération
        self.create_generation_tab()

        # Onglet Scanner (simulation)
        self.create_scanner_tab()

        # Onglet Impression
        self.create_printing_tab()

        self.content_layout.addWidget(self.tabs)

    def create_generation_tab(self):
        """Crée l'onglet de génération de codes-barres"""
        generation_widget = QWidget()
        layout = QVBoxLayout(generation_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("🏷️ Génération de Codes-barres")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Vérification des dépendances
        if not BARCODE_AVAILABLE:
            warning = QLabel("⚠️ Bibliothèque 'python-barcode' non installée.\nInstallez avec: pip install python-barcode[images]")
            warning.setStyleSheet("color: #ffaa00; background: rgba(255, 170, 0, 0.1); padding: 10px; border-radius: 5px;")
            layout.addWidget(warning)

        # Section sélection du produit
        product_group = QGroupBox("Sélection du produit")
        product_layout = QVBoxLayout(product_group)

        # Recherche de produit
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Rechercher:"))

        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("Code ou nom du produit...")
        self.product_search.textChanged.connect(self.search_products)
        search_layout.addWidget(self.product_search)

        product_layout.addLayout(search_layout)

        # Liste des produits
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.itemClicked.connect(self.select_product)
        product_layout.addWidget(self.products_list)

        layout.addWidget(product_group)

        # Section configuration du code-barres
        config_group = QGroupBox("Configuration du code-barres")
        config_layout = QVBoxLayout(config_group)

        # Type de code-barres
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type:"))

        self.barcode_type_combo = QComboBox()
        self.barcode_type_combo.addItems([
            "EAN13",
            "EAN8",
            "Code128",
            "Code39",
            "UPCA"
        ])
        type_layout.addWidget(self.barcode_type_combo)
        type_layout.addStretch()

        config_layout.addLayout(type_layout)

        # Code personnalisé
        code_layout = QHBoxLayout()
        code_layout.addWidget(QLabel("Code:"))

        self.barcode_code_input = QLineEdit()
        self.barcode_code_input.setPlaceholderText("Code automatique basé sur le produit")
        code_layout.addWidget(self.barcode_code_input)

        generate_code_button = QPushButton("🎲 Générer")
        generate_code_button.setObjectName("actionButton")
        generate_code_button.clicked.connect(self.generate_barcode_code)
        code_layout.addWidget(generate_code_button)

        config_layout.addLayout(code_layout)

        layout.addWidget(config_group)

        # Section aperçu
        preview_group = QGroupBox("Aperçu")
        preview_layout = QVBoxLayout(preview_group)

        self.barcode_preview = QLabel()
        self.barcode_preview.setAlignment(Qt.AlignCenter)
        self.barcode_preview.setMinimumHeight(150)
        self.barcode_preview.setStyleSheet("""
            QLabel {
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.05);
            }
        """)
        self.barcode_preview.setText("Aperçu du code-barres")
        preview_layout.addWidget(self.barcode_preview)

        layout.addWidget(preview_group)

        # Boutons d'action
        action_layout = QHBoxLayout()

        generate_button = QPushButton("🏷️ Générer Code-barres")
        generate_button.setObjectName("primaryButton")
        generate_button.clicked.connect(self.generate_barcode)
        action_layout.addWidget(generate_button)

        save_button = QPushButton("💾 Sauvegarder")
        save_button.setObjectName("actionButton")
        save_button.clicked.connect(self.save_barcode)
        action_layout.addWidget(save_button)

        action_layout.addStretch()

        layout.addLayout(action_layout)

        # Charger les produits
        self.load_products()

        self.tabs.addTab(generation_widget, "Génération")

    def create_scanner_tab(self):
        """Crée l'onglet de scanner (simulation)"""
        scanner_widget = QWidget()
        layout = QVBoxLayout(scanner_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("📷 Scanner de Codes-barres")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Note sur la simulation
        note = QLabel(
            "📝 Note: Cette interface simule un scanner de codes-barres.\n"
            "Dans un environnement de production, elle serait connectée à un scanner physique."
        )
        note.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-style: italic; margin-bottom: 20px;")
        note.setWordWrap(True)
        layout.addWidget(note)

        # Section saisie manuelle
        input_group = QGroupBox("Saisie manuelle du code-barres")
        input_layout = QVBoxLayout(input_group)

        scan_layout = QHBoxLayout()
        scan_layout.addWidget(QLabel("Code:"))

        self.scan_input = QLineEdit()
        self.scan_input.setPlaceholderText("Saisissez ou scannez un code-barres...")
        self.scan_input.returnPressed.connect(self.process_scanned_code)
        scan_layout.addWidget(self.scan_input)

        scan_button = QPushButton("🔍 Rechercher")
        scan_button.setObjectName("actionButton")
        scan_button.clicked.connect(self.process_scanned_code)
        scan_layout.addWidget(scan_button)

        input_layout.addLayout(scan_layout)

        layout.addWidget(input_group)

        # Section résultats
        results_group = QGroupBox("Résultats de la recherche")
        results_layout = QVBoxLayout(results_group)

        self.scan_results = QTextEdit()
        self.scan_results.setMaximumHeight(200)
        self.scan_results.setPlaceholderText("Les résultats de la recherche s'afficheront ici...")
        results_layout.addWidget(self.scan_results)

        layout.addWidget(results_group)

        # Section actions rapides
        actions_group = QGroupBox("Actions rapides")
        actions_layout = QVBoxLayout(actions_group)

        quick_actions_layout = QHBoxLayout()

        add_stock_button = QPushButton("📦 Ajouter au stock")
        add_stock_button.setObjectName("actionButton")
        add_stock_button.clicked.connect(self.quick_add_stock)
        quick_actions_layout.addWidget(add_stock_button)

        remove_stock_button = QPushButton("📤 Sortir du stock")
        remove_stock_button.setObjectName("actionButton")
        remove_stock_button.clicked.connect(self.quick_remove_stock)
        quick_actions_layout.addWidget(remove_stock_button)

        view_product_button = QPushButton("👁️ Voir produit")
        view_product_button.setObjectName("actionButton")
        view_product_button.clicked.connect(self.view_scanned_product)
        quick_actions_layout.addWidget(view_product_button)

        actions_layout.addLayout(quick_actions_layout)

        layout.addWidget(actions_group)

        # Historique des scans
        history_group = QGroupBox("Historique des scans")
        history_layout = QVBoxLayout(history_group)

        self.scan_history = QListWidget()
        self.scan_history.setMaximumHeight(150)
        history_layout.addWidget(self.scan_history)

        clear_history_button = QPushButton("🗑️ Vider l'historique")
        clear_history_button.setObjectName("actionButton")
        clear_history_button.clicked.connect(self.clear_scan_history)
        history_layout.addWidget(clear_history_button)

        layout.addWidget(history_group)

        self.tabs.addTab(scanner_widget, "Scanner")

    def create_printing_tab(self):
        """Crée l'onglet d'impression"""
        printing_widget = QWidget()
        layout = QVBoxLayout(printing_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("🖨️ Impression d'Étiquettes")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Section sélection des produits
        selection_group = QGroupBox("Sélection des produits")
        selection_layout = QVBoxLayout(selection_group)

        # Filtres
        filters_layout = QHBoxLayout()

        filters_layout.addWidget(QLabel("Catégorie:"))
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes les catégories", None)
        filters_layout.addWidget(self.category_filter)

        filters_layout.addWidget(QLabel("Stock:"))
        self.stock_filter = QComboBox()
        self.stock_filter.addItems(["Tous", "En stock", "Stock bas", "Rupture"])
        filters_layout.addWidget(self.stock_filter)

        filters_layout.addStretch()

        selection_layout.addLayout(filters_layout)

        # Liste des produits à imprimer
        self.print_products_list = QListWidget()
        self.print_products_list.setSelectionMode(QAbstractItemView.MultiSelection)
        self.print_products_list.setMaximumHeight(200)
        selection_layout.addWidget(self.print_products_list)

        # Boutons de sélection
        select_buttons_layout = QHBoxLayout()

        select_all_button = QPushButton("✅ Tout sélectionner")
        select_all_button.setObjectName("actionButton")
        select_all_button.clicked.connect(self.select_all_products)
        select_buttons_layout.addWidget(select_all_button)

        select_none_button = QPushButton("❌ Tout désélectionner")
        select_none_button.setObjectName("actionButton")
        select_none_button.clicked.connect(self.select_no_products)
        select_buttons_layout.addWidget(select_none_button)

        select_buttons_layout.addStretch()

        selection_layout.addLayout(select_buttons_layout)

        layout.addWidget(selection_group)

        # Section configuration d'impression
        print_config_group = QGroupBox("Configuration d'impression")
        print_config_layout = QVBoxLayout(print_config_group)

        # Format d'étiquette
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("Format:"))

        self.label_format_combo = QComboBox()
        self.label_format_combo.addItems([
            "Étiquette standard (50x30mm)",
            "Étiquette prix (40x25mm)",
            "Étiquette produit (60x40mm)",
            "Format personnalisé"
        ])
        format_layout.addWidget(self.label_format_combo)
        format_layout.addStretch()

        print_config_layout.addLayout(format_layout)

        # Options d'impression
        options_layout = QHBoxLayout()

        self.include_price = QCheckBox("Inclure le prix")
        self.include_price.setChecked(True)
        options_layout.addWidget(self.include_price)

        self.include_description = QCheckBox("Inclure la description")
        options_layout.addWidget(self.include_description)

        self.include_stock = QCheckBox("Inclure le stock")
        options_layout.addWidget(self.include_stock)

        print_config_layout.addLayout(options_layout)

        # Nombre de copies
        copies_layout = QHBoxLayout()
        copies_layout.addWidget(QLabel("Copies par produit:"))

        self.copies_spinbox = QSpinBox()
        self.copies_spinbox.setMinimum(1)
        self.copies_spinbox.setMaximum(100)
        self.copies_spinbox.setValue(1)
        copies_layout.addWidget(self.copies_spinbox)

        copies_layout.addStretch()

        print_config_layout.addLayout(copies_layout)

        layout.addWidget(print_config_group)

        # Boutons d'action
        print_actions_layout = QHBoxLayout()

        preview_print_button = QPushButton("👁️ Aperçu")
        preview_print_button.setObjectName("actionButton")
        preview_print_button.clicked.connect(self.preview_labels)
        print_actions_layout.addWidget(preview_print_button)

        print_labels_button = QPushButton("🖨️ Imprimer")
        print_labels_button.setObjectName("primaryButton")
        print_labels_button.clicked.connect(self.print_labels)
        print_actions_layout.addWidget(print_labels_button)

        print_actions_layout.addStretch()

        save_pdf_button = QPushButton("📄 Sauver PDF")
        save_pdf_button.setObjectName("actionButton")
        save_pdf_button.clicked.connect(self.save_labels_pdf)
        print_actions_layout.addWidget(save_pdf_button)

        layout.addLayout(print_actions_layout)

        # Charger les données pour l'impression
        self.load_print_data()

        self.tabs.addTab(printing_widget, "Impression")

    def load_products(self):
        """Charge la liste des produits"""
        try:
            with db_manager.get_session() as session:
                products = session.query(Product).filter(Product.is_active == True).all()

                self.products_list.clear()
                for product in products:
                    item_text = f"{product.code or 'N/A'} - {product.name}"
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.UserRole, product.id)
                    self.products_list.addItem(item)

        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement: {e}")

    def search_products(self):
        """Recherche des produits"""
        search_text = self.product_search.text().lower()

        for i in range(self.products_list.count()):
            item = self.products_list.item(i)
            item_text = item.text().lower()
            item.setHidden(search_text not in item_text)

    def select_product(self, item):
        """Sélectionne un produit"""
        product_id = item.data(Qt.UserRole)

        try:
            with db_manager.get_session() as session:
                product = session.query(Product).filter(Product.id == product_id).first()
                if product:
                    # Générer un code automatique basé sur le produit
                    auto_code = self.generate_product_barcode(product)
                    self.barcode_code_input.setText(auto_code)

        except Exception as e:
            self.show_message(f"Erreur: {e}", "error")

    def generate_product_barcode(self, product):
        """Génère un code-barres pour un produit"""
        # Utiliser le code produit ou générer un code EAN13
        if product.code and len(product.code) >= 8:
            # Utiliser le code produit comme base
            base_code = product.code.replace('-', '').replace('_', '')[:12]
            # Compléter avec des zéros si nécessaire
            base_code = base_code.ljust(12, '0')
            return base_code
        else:
            # Générer un code basé sur l'ID
            return f"200{product.id:09d}"

    def generate_barcode_code(self):
        """Génère un nouveau code-barres"""
        import random
        barcode_type = self.barcode_type_combo.currentText()

        if barcode_type == "EAN13":
            # Générer un code EAN13 aléatoire
            code = f"200{random.randint(100000000, 999999999)}"
        elif barcode_type == "EAN8":
            code = f"{random.randint(10000000, 99999999)}"
        else:
            code = f"PROD{random.randint(1000, 9999)}"

        self.barcode_code_input.setText(code)

    def generate_barcode(self):
        """Génère le code-barres"""
        if not BARCODE_AVAILABLE:
            QMessageBox.warning(
                self,
                "Bibliothèque manquante",
                "La bibliothèque python-barcode n'est pas installée.\n"
                "Installez-la avec: pip install python-barcode[images]"
            )
            return

        code = self.barcode_code_input.text().strip()
        if not code:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir un code.")
            return

        try:
            barcode_type = self.barcode_type_combo.currentText().lower()

            # Créer le code-barres
            barcode_class = barcode.get_barcode_class(barcode_type)
            barcode_instance = barcode_class(code, writer=ImageWriter())

            # Sauvegarder temporairement
            temp_path = f"temp_barcode_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            barcode_path = barcode_instance.save(temp_path)

            # Afficher dans l'aperçu
            pixmap = QPixmap(barcode_path)
            scaled_pixmap = pixmap.scaled(300, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.barcode_preview.setPixmap(scaled_pixmap)

            # Nettoyer le fichier temporaire
            if os.path.exists(barcode_path):
                os.remove(barcode_path)

            self.show_message("Code-barres généré avec succès", "success")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération: {e}")

    def save_barcode(self):
        """Sauvegarde le code-barres"""
        # TODO: Implémenter la sauvegarde
        QMessageBox.information(self, "Sauvegarde", "Fonctionnalité de sauvegarde en cours de développement")

    def process_scanned_code(self):
        """Traite un code scanné"""
        code = self.scan_input.text().strip()
        if not code:
            return

        try:
            with db_manager.get_session() as session:
                # Rechercher le produit par code
                product = session.query(Product).filter(Product.code == code).first()

                if product:
                    result = f"✅ Produit trouvé:\n"
                    result += f"Code: {product.code or 'N/A'}\n"
                    result += f"Nom: {product.name}\n"
                    result += f"Prix: {product.price or 0} DA\n"
                    result += f"Stock: {getattr(product, 'stock_quantity', 0)}\n"

                    self.current_scanned_product = product
                else:
                    result = f"❌ Aucun produit trouvé pour le code: {code}"
                    self.current_scanned_product = None

                self.scan_results.setText(result)

                # Ajouter à l'historique
                timestamp = datetime.now().strftime("%H:%M:%S")
                history_item = f"[{timestamp}] {code}"
                self.scan_history.addItem(history_item)

                # Vider le champ de saisie
                self.scan_input.clear()

        except Exception as e:
            self.scan_results.setText(f"❌ Erreur lors de la recherche: {e}")

    def quick_add_stock(self):
        """Ajoute rapidement du stock"""
        if hasattr(self, 'current_scanned_product') and self.current_scanned_product:
            # TODO: Ouvrir un dialogue pour ajouter du stock
            QMessageBox.information(self, "Stock", "Fonctionnalité d'ajout de stock en cours de développement")

    def quick_remove_stock(self):
        """Retire rapidement du stock"""
        if hasattr(self, 'current_scanned_product') and self.current_scanned_product:
            # TODO: Ouvrir un dialogue pour retirer du stock
            QMessageBox.information(self, "Stock", "Fonctionnalité de retrait de stock en cours de développement")

    def view_scanned_product(self):
        """Affiche les détails du produit scanné"""
        if hasattr(self, 'current_scanned_product') and self.current_scanned_product:
            # TODO: Ouvrir la fiche produit
            QMessageBox.information(self, "Produit", "Fonctionnalité d'affichage produit en cours de développement")

    def clear_scan_history(self):
        """Vide l'historique des scans"""
        self.scan_history.clear()

    def load_print_data(self):
        """Charge les données pour l'impression"""
        try:
            with db_manager.get_session() as session:
                # Charger les catégories
                from src.dal.models.product import Category
                categories = session.query(Category).all()

                for category in categories:
                    self.category_filter.addItem(category.name, category.id)

                # Charger les produits
                products = session.query(Product).filter(Product.is_active == True).all()

                self.print_products_list.clear()
                for product in products:
                    stock = getattr(product, 'stock_quantity', 0)
                    item_text = f"{product.code or 'N/A'} - {product.name} (Stock: {stock})"
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.UserRole, product.id)
                    self.print_products_list.addItem(item)

        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement: {e}")

    def show_message(self, message, message_type="info"):
        """Affiche un message à l'utilisateur"""
        if message_type == "success":
            QMessageBox.information(self, "Succès", message)
        elif message_type == "error":
            QMessageBox.critical(self, "Erreur", message)
        else:
            QMessageBox.information(self, "Information", message)

    def select_all_products(self):
        """Sélectionne tous les produits"""
        for i in range(self.print_products_list.count()):
            item = self.print_products_list.item(i)
            item.setSelected(True)

    def select_no_products(self):
        """Désélectionne tous les produits"""
        self.print_products_list.clearSelection()

    def preview_labels(self):
        """Affiche un aperçu des étiquettes"""
        selected_items = self.print_products_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner au moins un produit.")
            return

        QMessageBox.information(self, "Aperçu", f"Aperçu de {len(selected_items)} étiquettes en cours de développement")

    def print_labels(self):
        """Imprime les étiquettes"""
        selected_items = self.print_products_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner au moins un produit.")
            return

        QMessageBox.information(self, "Impression", f"Impression de {len(selected_items)} étiquettes en cours de développement")

    def save_labels_pdf(self):
        """Sauvegarde les étiquettes en PDF"""
        selected_items = self.print_products_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner au moins un produit.")
            return

        QMessageBox.information(self, "PDF", f"Sauvegarde PDF de {len(selected_items)} étiquettes en cours de développement")
