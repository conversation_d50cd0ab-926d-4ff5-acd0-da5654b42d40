#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module d'import/export de données
Interface pour importer et exporter des données en masse
"""

import os
import csv
import json
import logging
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget
from src.dal.database import db_manager
from src.dal.models.client import Client, ClientType
from src.dal.models.product import Product, Category, ProductStatus
from src.dal.models.client import Supplier

class ImportExportWidget(ModuleWidget):
    """Widget principal pour l'import/export"""

    def __init__(self, parent=None):
        super().__init__("Import / Export", parent)
        self.setup_import_export_ui()

    def setup_import_export_ui(self):
        """Configure l'interface spécifique à l'import/export"""
        # Remplacer le tableau par une interface d'import/export
        self.content_layout.removeWidget(self.data_table)
        self.data_table.hide()
        self.data_table.deleteLater()

        # Créer l'interface d'import/export
        self.create_import_export_interface()

    def create_import_export_interface(self):
        """Crée l'interface d'import/export"""
        # Layout principal avec onglets
        self.tabs = QTabWidget()
        self.tabs.setObjectName("importExportTabs")

        # Appliquer des styles pour améliorer la visibilité
        try:
            from src.ui.styles.module_styles import get_module_styles, get_import_export_specific_styles
            combined_styles = get_module_styles() + get_import_export_specific_styles()
            self.tabs.setStyleSheet(combined_styles)
        except ImportError:
            # Fallback styles si le module de styles n'est pas disponible
            self.tabs.setStyleSheet("""
                QTabWidget::pane {
                    border: 2px solid rgba(0, 212, 255, 0.3);
                    border-radius: 12px;
                    background: rgba(255, 255, 255, 0.08);
                    margin-top: 8px;
                    padding: 10px;
                }

                QTabBar::tab {
                    background: rgba(255, 255, 255, 0.15);
                    border: 1px solid rgba(255, 255, 255, 0.25);
                    border-bottom: none;
                    border-radius: 10px 10px 0 0;
                    padding: 12px 20px;
                    margin-right: 3px;
                    color: rgba(255, 255, 255, 0.85);
                    font-weight: 600;
                    min-width: 100px;
                }

                QTabBar::tab:selected {
                    background: rgba(0, 212, 255, 0.4);
                    border-color: #00d4ff;
                    color: #ffffff;
                    font-weight: bold;
                    border-bottom: 2px solid #00d4ff;
                }

                QTabBar::tab:hover:!selected {
                    background: rgba(255, 255, 255, 0.2);
                    color: #ffffff;
                    border-color: rgba(255, 255, 255, 0.4);
                }

                QGroupBox {
                    font-weight: bold;
                    border: 2px solid rgba(0, 212, 255, 0.3);
                    border-radius: 10px;
                    margin-top: 15px;
                    padding-top: 15px;
                    color: #00d4ff;
                    background: rgba(255, 255, 255, 0.05);
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 15px;
                    padding: 0 8px 0 8px;
                    color: #00d4ff;
                    font-weight: bold;
                    font-size: 14px;
                    background: rgba(0, 212, 255, 0.2);
                    border-radius: 5px;
                }
            """)

        # Onglet Import
        self.create_import_tab()

        # Onglet Export
        self.create_export_tab()

        # Onglet Modèles
        self.create_templates_tab()

        self.content_layout.addWidget(self.tabs)

    def create_import_tab(self):
        """Crée l'onglet d'import"""
        import_widget = QWidget()
        layout = QVBoxLayout(import_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("📥 Import de Données")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Section sélection du type
        type_group = QGroupBox("Type de données à importer")
        type_layout = QVBoxLayout(type_group)

        self.import_type_combo = QComboBox()
        self.import_type_combo.addItems([
            "Clients",
            "Fournisseurs",
            "Produits",
            "Catégories"
        ])
        type_layout.addWidget(self.import_type_combo)

        layout.addWidget(type_group)

        # Section sélection du fichier
        file_group = QGroupBox("Fichier à importer")
        file_layout = QVBoxLayout(file_group)

        file_selection_layout = QHBoxLayout()
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("Sélectionner un fichier CSV...")
        file_selection_layout.addWidget(self.file_path_input)

        browse_button = QPushButton("📁 Parcourir")
        browse_button.setObjectName("actionButton")
        browse_button.clicked.connect(self.browse_import_file)
        file_selection_layout.addWidget(browse_button)

        file_layout.addLayout(file_selection_layout)

        # Options d'import
        options_layout = QHBoxLayout()

        self.skip_first_row = QCheckBox("Ignorer la première ligne (en-têtes)")
        self.skip_first_row.setChecked(True)
        options_layout.addWidget(self.skip_first_row)

        self.update_existing = QCheckBox("Mettre à jour les enregistrements existants")
        options_layout.addWidget(self.update_existing)

        file_layout.addLayout(options_layout)

        layout.addWidget(file_group)

        # Aperçu des données
        preview_group = QGroupBox("Aperçu des données")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_table = QTableWidget()
        self.preview_table.setMaximumHeight(200)
        preview_layout.addWidget(self.preview_table)

        preview_button = QPushButton("🔍 Aperçu")
        preview_button.setObjectName("actionButton")
        preview_button.clicked.connect(self.preview_import_data)
        preview_layout.addWidget(preview_button)

        layout.addWidget(preview_group)

        # Boutons d'action
        action_layout = QHBoxLayout()

        import_button = QPushButton("📥 Importer")
        import_button.setObjectName("primaryButton")
        import_button.clicked.connect(self.import_data)
        action_layout.addWidget(import_button)

        action_layout.addStretch()

        download_template_button = QPushButton("📄 Télécharger modèle")
        download_template_button.setObjectName("actionButton")
        download_template_button.clicked.connect(self.download_template)
        action_layout.addWidget(download_template_button)

        layout.addLayout(action_layout)

        # Zone de résultats
        self.import_results = QTextEdit()
        self.import_results.setMaximumHeight(150)
        self.import_results.setPlaceholderText("Les résultats d'import s'afficheront ici...")
        layout.addWidget(self.import_results)

        self.tabs.addTab(import_widget, "Import")

    def create_export_tab(self):
        """Crée l'onglet d'export"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("📤 Export de Données")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Section sélection du type
        type_group = QGroupBox("Type de données à exporter")
        type_layout = QVBoxLayout(type_group)

        self.export_type_combo = QComboBox()
        self.export_type_combo.addItems([
            "Clients",
            "Fournisseurs",
            "Produits",
            "Catégories",
            "Mouvements de stock",
            "Devis",
            "Commandes",
            "Factures"
        ])
        type_layout.addWidget(self.export_type_combo)

        layout.addWidget(type_group)

        # Section format d'export
        format_group = QGroupBox("Format d'export")
        format_layout = QVBoxLayout(format_group)

        format_selection_layout = QHBoxLayout()

        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems([
            "CSV (Excel)",
            "JSON",
            "PDF (Rapport)"
        ])
        format_selection_layout.addWidget(self.export_format_combo)

        format_layout.addLayout(format_selection_layout)

        # Options d'export
        options_layout = QHBoxLayout()

        self.include_headers = QCheckBox("Inclure les en-têtes")
        self.include_headers.setChecked(True)
        options_layout.addWidget(self.include_headers)

        self.active_only = QCheckBox("Éléments actifs uniquement")
        self.active_only.setChecked(True)
        options_layout.addWidget(self.active_only)

        format_layout.addLayout(options_layout)

        layout.addWidget(format_group)

        # Section filtres
        filters_group = QGroupBox("Filtres (optionnel)")
        filters_layout = QVBoxLayout(filters_group)

        date_filter_layout = QHBoxLayout()
        date_filter_layout.addWidget(QLabel("Période:"))

        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setDisplayFormat("dd/MM/yyyy")
        date_filter_layout.addWidget(self.date_from)

        date_filter_layout.addWidget(QLabel("à"))

        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setDisplayFormat("dd/MM/yyyy")
        date_filter_layout.addWidget(self.date_to)

        date_filter_layout.addStretch()
        filters_layout.addLayout(date_filter_layout)

        layout.addWidget(filters_group)

        # Boutons d'action
        action_layout = QHBoxLayout()

        export_button = QPushButton("📤 Exporter")
        export_button.setObjectName("primaryButton")
        export_button.clicked.connect(self.export_data)
        action_layout.addWidget(export_button)

        action_layout.addStretch()

        open_folder_button = QPushButton("📁 Ouvrir dossier")
        open_folder_button.setObjectName("actionButton")
        open_folder_button.clicked.connect(self.open_export_folder)
        action_layout.addWidget(open_folder_button)

        layout.addLayout(action_layout)

        # Zone de résultats
        self.export_results = QTextEdit()
        self.export_results.setMaximumHeight(150)
        self.export_results.setPlaceholderText("Les résultats d'export s'afficheront ici...")
        layout.addWidget(self.export_results)

        self.tabs.addTab(export_widget, "Export")

    def create_templates_tab(self):
        """Crée l'onglet des modèles"""
        templates_widget = QWidget()
        layout = QVBoxLayout(templates_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Titre
        title = QLabel("📋 Modèles d'Import")
        title.setObjectName("reportTitle")
        layout.addWidget(title)

        # Description
        description = QLabel(
            "Téléchargez les modèles CSV pour importer vos données.\n"
            "Les modèles contiennent les colonnes requises et des exemples de données."
        )
        description.setStyleSheet("color: rgba(255, 255, 255, 0.7); margin-bottom: 20px;")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Liste des modèles
        templates_list = QListWidget()
        templates_list.setObjectName("templatesList")

        templates = [
            ("👥 Modèle Clients", "Template pour importer des clients"),
            ("🏭 Modèle Fournisseurs", "Template pour importer des fournisseurs"),
            ("📦 Modèle Produits", "Template pour importer des produits"),
            ("📂 Modèle Catégories", "Template pour importer des catégories")
        ]

        for title_text, description_text in templates:
            item = QListWidgetItem()
            item.setText(f"{title_text}\n{description_text}")
            item.setData(Qt.UserRole, title_text.split()[1])  # Stocker le type
            templates_list.addItem(item)

        layout.addWidget(templates_list)

        # Boutons
        buttons_layout = QHBoxLayout()

        download_selected_button = QPushButton("📥 Télécharger sélectionné")
        download_selected_button.setObjectName("actionButton")
        download_selected_button.clicked.connect(
            lambda: self.download_selected_template(templates_list)
        )
        buttons_layout.addWidget(download_selected_button)

        download_all_button = QPushButton("📥 Télécharger tous")
        download_all_button.setObjectName("actionButton")
        download_all_button.clicked.connect(self.download_all_templates)
        buttons_layout.addWidget(download_all_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        self.tabs.addTab(templates_widget, "Modèles")

    def browse_import_file(self):
        """Ouvre le dialogue de sélection de fichier"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner un fichier à importer",
            "",
            "Fichiers CSV (*.csv);;Tous les fichiers (*)"
        )

        if file_path:
            self.file_path_input.setText(file_path)

    def preview_import_data(self):
        """Affiche un aperçu des données à importer"""
        file_path = self.file_path_input.text().strip()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fichier valide.")
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                rows = list(reader)

            if not rows:
                QMessageBox.warning(self, "Erreur", "Le fichier est vide.")
                return

            # Configurer le tableau d'aperçu
            start_row = 1 if self.skip_first_row.isChecked() and len(rows) > 1 else 0
            preview_rows = rows[start_row:start_row + 10]  # Afficher max 10 lignes

            if rows and self.skip_first_row.isChecked():
                headers = rows[0]
                self.preview_table.setColumnCount(len(headers))
                self.preview_table.setHorizontalHeaderLabels(headers)
            else:
                max_cols = max(len(row) for row in preview_rows) if preview_rows else 0
                self.preview_table.setColumnCount(max_cols)

            self.preview_table.setRowCount(len(preview_rows))

            for row_idx, row in enumerate(preview_rows):
                for col_idx, cell in enumerate(row):
                    self.preview_table.setItem(row_idx, col_idx, QTableWidgetItem(str(cell)))

            self.preview_table.resizeColumnsToContents()
            self.show_message(f"Aperçu: {len(preview_rows)} lignes affichées", "success")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la lecture du fichier: {e}")

    def import_data(self):
        """Importe les données depuis le fichier"""
        file_path = self.file_path_input.text().strip()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fichier valide.")
            return

        data_type = self.import_type_combo.currentText()

        try:
            if data_type == "Clients":
                result = self.import_clients(file_path)
            elif data_type == "Fournisseurs":
                result = self.import_suppliers(file_path)
            elif data_type == "Produits":
                result = self.import_products(file_path)
            elif data_type == "Catégories":
                result = self.import_categories(file_path)
            else:
                QMessageBox.warning(self, "Erreur", "Type de données non supporté.")
                return

            self.import_results.setText(result)
            self.show_message("Import terminé", "success")

        except Exception as e:
            error_msg = f"Erreur lors de l'import: {e}"
            self.import_results.setText(error_msg)
            self.show_message("Erreur lors de l'import", "error")

    def import_clients(self, file_path):
        """Importe les clients depuis un fichier CSV"""
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file) if self.skip_first_row.isChecked() else csv.reader(file)

            imported = 0
            updated = 0
            errors = []

            with db_manager.get_session() as session:
                for row_num, row in enumerate(reader, 1):
                    try:
                        if isinstance(row, dict):
                            # Mode DictReader
                            client_data = {
                                'code': row.get('code', ''),
                                'name': row.get('name', ''),
                                'email': row.get('email', ''),
                                'phone': row.get('phone', ''),
                                'client_type': ClientType.INDIVIDUAL
                            }
                        else:
                            # Mode reader normal
                            if len(row) < 4:
                                continue
                            client_data = {
                                'code': row[0],
                                'name': row[1],
                                'email': row[2],
                                'phone': row[3],
                                'client_type': ClientType.INDIVIDUAL
                            }

                        # Vérifier si le client existe
                        existing = session.query(Client).filter(
                            Client.code == client_data['code']
                        ).first()

                        if existing and self.update_existing.isChecked():
                            for key, value in client_data.items():
                                setattr(existing, key, value)
                            updated += 1
                        elif not existing:
                            client = Client(**client_data)
                            session.add(client)
                            imported += 1

                    except Exception as e:
                        errors.append(f"Ligne {row_num}: {e}")

                session.commit()

            result = f"Import terminé:\n"
            result += f"• {imported} clients importés\n"
            result += f"• {updated} clients mis à jour\n"
            if errors:
                result += f"• {len(errors)} erreurs:\n"
                result += "\n".join(errors[:10])  # Afficher max 10 erreurs

            return result

    def import_suppliers(self, file_path):
        """Importe les fournisseurs depuis un fichier CSV"""
        # TODO: Implémenter l'import des fournisseurs
        return "Import des fournisseurs en cours de développement"

    def import_products(self, file_path):
        """Importe les produits depuis un fichier CSV"""
        # TODO: Implémenter l'import des produits
        return "Import des produits en cours de développement"

    def import_categories(self, file_path):
        """Importe les catégories depuis un fichier CSV"""
        # TODO: Implémenter l'import des catégories
        return "Import des catégories en cours de développement"

    def export_data(self):
        """Exporte les données"""
        data_type = self.export_type_combo.currentText()
        export_format = self.export_format_combo.currentText()

        try:
            if data_type == "Clients":
                result = self.export_clients(export_format)
            elif data_type == "Produits":
                result = self.export_products(export_format)
            else:
                result = f"Export de {data_type} en cours de développement"

            self.export_results.setText(result)
            self.show_message("Export terminé", "success")

        except Exception as e:
            error_msg = f"Erreur lors de l'export: {e}"
            self.export_results.setText(error_msg)
            self.show_message("Erreur lors de l'export", "error")

    def export_clients(self, export_format):
        """Exporte les clients"""
        try:
            with db_manager.get_session() as session:
                query = session.query(Client)
                if self.active_only.isChecked():
                    query = query.filter(Client.is_active == True)

                clients = query.all()

                if export_format.startswith("CSV"):
                    return self.export_clients_csv(clients)
                elif export_format.startswith("JSON"):
                    return self.export_clients_json(clients)
                else:
                    return "Format d'export non supporté"

        except Exception as e:
            return f"Erreur export clients: {e}"

    def export_clients_csv(self, clients):
        """Exporte les clients en CSV"""
        try:
            # Créer le dossier exports s'il n'existe pas
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)

            # Nom du fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"clients_export_{timestamp}.csv"
            filepath = os.path.join(exports_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)

                # En-têtes
                if self.include_headers.isChecked():
                    writer.writerow(['Code', 'Nom', 'Email', 'Téléphone', 'Type', 'Actif'])

                # Données
                for client in clients:
                    writer.writerow([
                        client.code or '',
                        client.name,
                        client.email or '',
                        client.phone or '',
                        client.client_type.value if client.client_type else '',
                        'Oui' if client.is_active else 'Non'
                    ])

            return f"Export CSV réussi:\n• {len(clients)} clients exportés\n• Fichier: {filename}"

        except Exception as e:
            return f"Erreur export CSV: {e}"

    def export_clients_json(self, clients):
        """Exporte les clients en JSON"""
        try:
            # Créer le dossier exports s'il n'existe pas
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)

            # Nom du fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"clients_export_{timestamp}.json"
            filepath = os.path.join(exports_dir, filename)

            # Préparer les données
            clients_data = []
            for client in clients:
                client_dict = {
                    'code': client.code,
                    'name': client.name,
                    'email': client.email,
                    'phone': client.phone,
                    'type': client.client_type.value if client.client_type else None,
                    'is_active': client.is_active,
                    'created_at': client.created_at.isoformat() if client.created_at else None
                }
                clients_data.append(client_dict)

            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(clients_data, file, indent=2, ensure_ascii=False)

            return f"Export JSON réussi:\n• {len(clients)} clients exportés\n• Fichier: {filename}"

        except Exception as e:
            return f"Erreur export JSON: {e}"

    def export_products(self, export_format):
        """Exporte les produits"""
        try:
            with db_manager.get_session() as session:
                query = session.query(Product)
                if self.active_only.isChecked():
                    query = query.filter(Product.is_active == True)

                products = query.all()

                if export_format.startswith("CSV"):
                    return self.export_products_csv(products)
                elif export_format.startswith("JSON"):
                    return self.export_products_json(products)
                else:
                    return "Format d'export non supporté"

        except Exception as e:
            return f"Erreur export produits: {e}"

    def export_products_csv(self, products):
        """Exporte les produits en CSV"""
        try:
            # Créer le dossier exports s'il n'existe pas
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)

            # Nom du fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"produits_export_{timestamp}.csv"
            filepath = os.path.join(exports_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)

                # En-têtes
                if self.include_headers.isChecked():
                    writer.writerow(['Code', 'Nom', 'Description', 'Prix', 'Stock', 'Actif'])

                # Données
                for product in products:
                    writer.writerow([
                        product.code or '',
                        product.name,
                        product.description or '',
                        product.price or 0,
                        getattr(product, 'stock_quantity', 0),
                        'Oui' if product.is_active else 'Non'
                    ])

            return f"Export CSV réussi:\n• {len(products)} produits exportés\n• Fichier: {filename}"

        except Exception as e:
            return f"Erreur export CSV: {e}"

    def export_products_json(self, products):
        """Exporte les produits en JSON"""
        try:
            # Créer le dossier exports s'il n'existe pas
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)

            # Nom du fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"produits_export_{timestamp}.json"
            filepath = os.path.join(exports_dir, filename)

            # Préparer les données
            products_data = []
            for product in products:
                product_dict = {
                    'code': product.code,
                    'name': product.name,
                    'description': product.description,
                    'price': float(product.price) if product.price else 0,
                    'stock_quantity': getattr(product, 'stock_quantity', 0),
                    'is_active': product.is_active,
                    'created_at': product.created_at.isoformat() if product.created_at else None
                }
                products_data.append(product_dict)

            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(products_data, file, indent=2, ensure_ascii=False)

            return f"Export JSON réussi:\n• {len(products)} produits exportés\n• Fichier: {filename}"

        except Exception as e:
            return f"Erreur export JSON: {e}"

    def show_message(self, message, message_type="info"):
        """Affiche un message à l'utilisateur"""
        if message_type == "success":
            QMessageBox.information(self, "Succès", message)
        elif message_type == "error":
            QMessageBox.critical(self, "Erreur", message)
        else:
            QMessageBox.information(self, "Information", message)

    def open_export_folder(self):
        """Ouvre le dossier d'export"""
        exports_dir = os.path.abspath("exports")
        if os.path.exists(exports_dir):
            if os.name == 'nt':  # Windows
                os.startfile(exports_dir)
            else:  # Linux/Mac
                import subprocess
                subprocess.call(['xdg-open', exports_dir])
        else:
            QMessageBox.information(self, "Dossier", "Le dossier d'export n'existe pas encore.")

    def download_template(self):
        """Télécharge le modèle pour le type sélectionné"""
        data_type = self.import_type_combo.currentText()
        self.create_template(data_type)

    def download_selected_template(self, templates_list):
        """Télécharge le modèle sélectionné"""
        current_item = templates_list.currentItem()
        if current_item:
            template_type = current_item.data(Qt.UserRole)
            self.create_template(template_type)
        else:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un modèle.")

    def download_all_templates(self):
        """Télécharge tous les modèles"""
        templates = ["Clients", "Fournisseurs", "Produits", "Catégories"]
        for template in templates:
            self.create_template(template)

        QMessageBox.information(self, "Modèles", f"{len(templates)} modèles téléchargés.")

    def create_template(self, template_type):
        """Crée un fichier modèle CSV"""
        try:
            # Créer le dossier templates s'il n'existe pas
            templates_dir = "templates"
            os.makedirs(templates_dir, exist_ok=True)

            filename = f"modele_{template_type.lower()}.csv"
            filepath = os.path.join(templates_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)

                if template_type == "Clients":
                    writer.writerow(['code', 'name', 'email', 'phone'])
                    writer.writerow(['CLI001', 'Exemple Client', '<EMAIL>', '0123456789'])
                elif template_type == "Fournisseurs":
                    writer.writerow(['code', 'name', 'email', 'phone'])
                    writer.writerow(['FOUR001', 'Exemple Fournisseur', '<EMAIL>', '0123456789'])
                elif template_type == "Produits":
                    writer.writerow(['code', 'name', 'description', 'price'])
                    writer.writerow(['PROD001', 'Exemple Produit', 'Description du produit', '100.00'])
                elif template_type == "Catégories":
                    writer.writerow(['name', 'description'])
                    writer.writerow(['Exemple Catégorie', 'Description de la catégorie'])

            QMessageBox.information(self, "Modèle", f"Modèle {template_type} créé: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur création modèle: {e}")
