#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Thème système pour GSCOM
Détecte automatiquement les préférences du système d'exploitation
"""

from typing import Dict
from .base_theme import BaseTheme
from .dark_theme import DarkTheme
from .light_theme import LightTheme


class SystemTheme(BaseTheme):
    """Thème système qui s'adapte aux préférences OS"""
    
    def __init__(self):
        self.name = "system"
        self.current_theme = self.detect_system_theme()
        self.colors = self.current_theme.colors
        self.styles = self.current_theme.styles
    
    def detect_system_theme(self):
        """Détecte le thème système actuel"""
        try:
            # Méthode 1: Registre Windows
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, 
                r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize"
            )
            value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            winreg.<PERSON><PERSON><PERSON>(key)
            
            if value == 0:  # Thème sombre
                return DarkTheme()
            else:  # Thème clair
                return LightTheme()
                
        except Exception:
            try:
                # Méthode 2: Variables d'environnement
                import os
                theme_env = os.environ.get('THEME', '').lower()
                if 'dark' in theme_env:
                    return DarkTheme()
                elif 'light' in theme_env:
                    return LightTheme()
                    
                # Méthode 3: Heure du jour (fallback intelligent)
                from datetime import datetime
                current_hour = datetime.now().hour
                
                # Thème sombre entre 18h et 8h
                if current_hour >= 18 or current_hour <= 8:
                    return DarkTheme()
                else:
                    return LightTheme()
                    
            except Exception:
                # Fallback par défaut
                return DarkTheme()
    
    def refresh_theme(self):
        """Actualise le thème selon les préférences système"""
        new_theme = self.detect_system_theme()
        
        # Mettre à jour seulement si le thème a changé
        if type(new_theme) != type(self.current_theme):
            self.current_theme = new_theme
            self.colors = new_theme.colors
            self.styles = new_theme.styles
            return True
        return False
    
    def get_colors(self) -> Dict[str, str]:
        """Retourne les couleurs du thème système actuel"""
        return self.current_theme.get_colors()
    
    def get_styles(self) -> str:
        """Retourne les styles CSS du thème système actuel"""
        return self.current_theme.get_styles()
    
    def is_dark(self) -> bool:
        """Indique si le thème système actuel est sombre"""
        return self.current_theme.is_dark()
    
    def get_theme_name(self) -> str:
        """Retourne le nom du thème système détecté"""
        return f"system ({self.current_theme.name})"
    
    def get_detection_info(self) -> Dict[str, str]:
        """Retourne des informations sur la détection du thème"""
        try:
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, 
                r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize"
            )
            apps_light, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            system_light, _ = winreg.QueryValueEx(key, "SystemUsesLightTheme")
            winreg.CloseKey(key)
            
            return {
                "method": "Windows Registry",
                "apps_light_theme": str(bool(apps_light)),
                "system_light_theme": str(bool(system_light)),
                "detected_theme": self.current_theme.name,
                "is_dark": str(self.is_dark())
            }
        except Exception as e:
            from datetime import datetime
            return {
                "method": "Fallback (Time-based)",
                "current_hour": str(datetime.now().hour),
                "detected_theme": self.current_theme.name,
                "is_dark": str(self.is_dark()),
                "error": str(e)
            }


class AutoSystemTheme(SystemTheme):
    """Thème système avec actualisation automatique"""
    
    def __init__(self):
        super().__init__()
        self.setup_auto_refresh()
    
    def setup_auto_refresh(self):
        """Configure l'actualisation automatique du thème"""
        try:
            from PyQt5.QtCore import QTimer
            
            # Timer pour vérifier les changements toutes les 30 secondes
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self.check_theme_change)
            self.refresh_timer.start(30000)  # 30 secondes
            
        except Exception:
            # Si PyQt5 n'est pas disponible, pas d'auto-refresh
            pass
    
    def check_theme_change(self):
        """Vérifie si le thème système a changé"""
        if self.refresh_theme():
            # Émettre un signal de changement si nécessaire
            try:
                from src.ui.styles.theme_manager import theme_manager
                theme_manager.theme_changed.emit("system")
            except Exception:
                pass


def create_system_theme(auto_refresh=True):
    """Factory function pour créer un thème système"""
    if auto_refresh:
        return AutoSystemTheme()
    else:
        return SystemTheme()
