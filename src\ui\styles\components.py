#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Styles pour les composants modernes GSCOM
Boutons, cartes, formulaires et autres éléments UI
"""

from typing import Dict


def get_button_styles(colors: Dict[str, str]) -> str:
    """Styles modernes pour les boutons"""
    return f"""
    /* === BOUTONS PRIMAIRES === */
    QPushButton[class="primary"] {{
        background: {colors.get('gradient_primary', colors.get('primary', '#0066cc'))};
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        color: {colors.get('text_inverse', '#ffffff')};
        font-weight: 600;
        font-size: 14px;
        min-height: 20px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }}
    
    QPushButton[class="primary"]:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px {colors.get('shadow_medium', 'rgba(0, 0, 0, 0.15)')};
    }}
    
    QPushButton[class="primary"]:pressed {{
        transform: translateY(0px);
        box-shadow: 0 4px 15px {colors.get('shadow_light', 'rgba(0, 0, 0, 0.1)')};
    }}
    
    /* === BOUTONS SECONDAIRES === */
    QPushButton[class="secondary"] {{
        background: {colors.get('surface', '#ffffff')};
        border: 2px solid {colors.get('primary', '#0066cc')};
        border-radius: 12px;
        padding: 10px 22px;
        color: {colors.get('primary', '#0066cc')};
        font-weight: 600;
        font-size: 14px;
        min-height: 20px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }}
    
    QPushButton[class="secondary"]:hover {{
        background: {colors.get('primary', '#0066cc')};
        color: {colors.get('text_inverse', '#ffffff')};
        transform: translateY(-1px);
    }}
    
    /* === BOUTONS OUTLINE === */
    QPushButton[class="outline"] {{
        background: transparent;
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 8px;
        padding: 10px 16px;
        color: {colors.get('text_primary', '#1e293b')};
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
    }}
    
    QPushButton[class="outline"]:hover {{
        background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
        border-color: {colors.get('primary', '#0066cc')};
    }}
    
    /* === BOUTONS ICON === */
    QPushButton[class="icon"] {{
        background: {colors.get('surface', '#ffffff')};
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 50%;
        padding: 12px;
        color: {colors.get('text_primary', '#1e293b')};
        font-size: 16px;
        min-width: 44px;
        min-height: 44px;
        max-width: 44px;
        max-height: 44px;
        transition: all 0.3s ease;
    }}
    
    QPushButton[class="icon"]:hover {{
        background: {colors.get('primary', '#0066cc')};
        color: {colors.get('text_inverse', '#ffffff')};
        transform: scale(1.1);
    }}
    
    /* === BOUTONS DANGER === */
    QPushButton[class="danger"] {{
        background: {colors.get('error', '#dc2626')};
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        color: {colors.get('text_inverse', '#ffffff')};
        font-weight: 600;
        font-size: 14px;
    }}
    
    QPushButton[class="danger"]:hover {{
        background: {colors.get('error_light', '#ef4444')};
    }}
    """


def get_card_styles(colors: Dict[str, str]) -> str:
    """Styles modernes pour les cartes"""
    return f"""
    /* === CARTES DE BASE === */
    QFrame[class="card"] {{
        background: {colors.get('surface', '#ffffff')};
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 16px;
        padding: 24px;
        margin: 8px;
        box-shadow: 0 2px 8px {colors.get('shadow_light', 'rgba(0, 0, 0, 0.05)')};
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }}
    
    QFrame[class="card"]:hover {{
        border-color: {colors.get('border_accent', colors.get('primary', '#0066cc'))};
        box-shadow: 0 8px 32px {colors.get('shadow_medium', 'rgba(0, 0, 0, 0.1)')};
        transform: translateY(-2px);
    }}
    
    /* === CARTES STATISTIQUES === */
    QFrame[class="stat-card"] {{
        background: {colors.get('gradient_surface', colors.get('surface', '#ffffff'))};
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 20px;
        padding: 20px;
        margin: 8px;
        min-height: 120px;
        transition: all 0.3s ease;
    }}
    
    QFrame[class="stat-card"]:hover {{
        transform: scale(1.02);
        box-shadow: 0 12px 40px {colors.get('shadow_medium', 'rgba(0, 0, 0, 0.1)')};
    }}
    
    /* === CARTES ÉLEVÉES === */
    QFrame[class="elevated-card"] {{
        background: {colors.get('surface_elevated', colors.get('surface', '#ffffff'))};
        border: none;
        border-radius: 24px;
        padding: 32px;
        box-shadow: 0 16px 64px {colors.get('shadow_dark', 'rgba(0, 0, 0, 0.15)')};
    }}
    """


def get_input_styles(colors: Dict[str, str]) -> str:
    """Styles modernes pour les champs de saisie"""
    return f"""
    /* === CHAMPS DE SAISIE === */
    QLineEdit, QTextEdit, QPlainTextEdit {{
        background: {colors.get('surface', '#ffffff')};
        border: 2px solid {colors.get('border', '#e2e8f0')};
        border-radius: 12px;
        padding: 12px 16px;
        color: {colors.get('text_primary', '#1e293b')};
        font-size: 14px;
        font-family: 'Segoe UI', sans-serif;
        transition: all 0.3s ease;
    }}
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border-color: {colors.get('primary', '#0066cc')};
        background: {colors.get('surface', '#ffffff')};
        box-shadow: 0 0 0 3px {colors.get('focus', 'rgba(0, 102, 204, 0.1)')};
    }}
    
    QLineEdit::placeholder {{
        color: {colors.get('text_muted', '#94a3b8')};
        font-style: italic;
    }}
    
    /* === COMBOBOX === */
    QComboBox {{
        background: {colors.get('surface', '#ffffff')};
        border: 2px solid {colors.get('border', '#e2e8f0')};
        border-radius: 12px;
        padding: 12px 16px;
        color: {colors.get('text_primary', '#1e293b')};
        font-size: 14px;
        min-height: 20px;
    }}
    
    QComboBox:focus {{
        border-color: {colors.get('primary', '#0066cc')};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 30px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {colors.get('text_secondary', '#64748b')};
        margin-right: 10px;
    }}
    
    QComboBox QAbstractItemView {{
        background: {colors.get('surface', '#ffffff')};
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 8px;
        selection-background-color: {colors.get('selected', 'rgba(0, 102, 204, 0.1)')};
        padding: 4px;
    }}
    """


def get_table_styles(colors: Dict[str, str]) -> str:
    """Styles modernes pour les tableaux"""
    return f"""
    /* === TABLEAUX === */
    QTableWidget {{
        background: {colors.get('surface', '#ffffff')};
        border: 1px solid {colors.get('border', '#e2e8f0')};
        border-radius: 12px;
        gridline-color: {colors.get('border_light', '#f1f5f9')};
        selection-background-color: {colors.get('selected', 'rgba(0, 102, 204, 0.1)')};
        font-size: 14px;
    }}
    
    QTableWidget::item {{
        padding: 16px 12px;
        border-bottom: 1px solid {colors.get('border_light', '#f1f5f9')};
        color: {colors.get('text_primary', '#1e293b')};
    }}
    
    QTableWidget::item:selected {{
        background: {colors.get('primary', '#0066cc')};
        color: {colors.get('text_inverse', '#ffffff')};
    }}
    
    QTableWidget::item:hover {{
        background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
    }}
    
    QHeaderView::section {{
        background: {colors.get('surface_variant', '#f1f5f9')};
        border: none;
        border-bottom: 2px solid {colors.get('primary', '#0066cc')};
        padding: 16px 12px;
        color: {colors.get('text_primary', '#1e293b')};
        font-weight: 600;
        font-size: 14px;
    }}
    
    QHeaderView::section:hover {{
        background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
    }}
    """


def get_scrollbar_styles(colors: Dict[str, str]) -> str:
    """Styles modernes pour les barres de défilement"""
    return f"""
    /* === BARRES DE DÉFILEMENT === */
    QScrollBar:vertical {{
        background: {colors.get('surface_variant', '#f1f5f9')};
        width: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:vertical {{
        background: {colors.get('primary', '#0066cc')};
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background: {colors.get('primary_dark', '#004499')};
    }}
    
    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {{
        height: 0;
        background: transparent;
    }}
    
    QScrollBar:horizontal {{
        background: {colors.get('surface_variant', '#f1f5f9')};
        height: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:horizontal {{
        background: {colors.get('primary', '#0066cc')};
        border-radius: 6px;
        min-width: 20px;
        margin: 2px;
    }}
    """


def get_modern_components_styles(colors: Dict[str, str]) -> str:
    """Combine tous les styles de composants modernes"""
    return (
        get_button_styles(colors) +
        get_card_styles(colors) +
        get_input_styles(colors) +
        get_table_styles(colors) +
        get_scrollbar_styles(colors)
    )
