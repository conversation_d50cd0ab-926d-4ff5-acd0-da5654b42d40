/* ===== VARIABLES CSS POUR LES THÈMES ===== */
:root {
  /* Thème clair */
  --primary: #0066cc;
  --primary-dark: #004499;
  --primary-light: #3388dd;
  --secondary: #6366f1;
  --success: #059669;
  --warning: #d97706;
  --error: #dc2626;
  --info: #2563eb;
  
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --surface: #ffffff;
  --surface-variant: #f1f5f9;
  --surface-elevated: #ffffff;
  
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  --text-muted: #94a3b8;
  
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --border-dark: #cbd5e1;
  
  --hover: rgba(0, 102, 204, 0.08);
  --active: rgba(0, 102, 204, 0.16);
  --focus: rgba(0, 102, 204, 0.24);
  --selected: rgba(0, 102, 204, 0.12);
  
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-medium: rgba(0, 0, 0, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.15);
  
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Thème sombre */
.theme-dark {
  --primary: #00d4ff;
  --primary-dark: #0099cc;
  --primary-light: #33ddff;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  --background: #0f0f23;
  --background-secondary: #1a1a2e;
  --surface: #16213e;
  --surface-variant: #1e2749;
  --surface-elevated: #252d4a;
  
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #94a3b8;
  --text-inverse: #0f0f23;
  --text-muted: #64748b;
  
  --border: rgba(255, 255, 255, 0.12);
  --border-light: rgba(255, 255, 255, 0.08);
  --border-dark: rgba(255, 255, 255, 0.16);
  
  --hover: rgba(0, 212, 255, 0.12);
  --active: rgba(0, 212, 255, 0.24);
  --focus: rgba(0, 212, 255, 0.32);
  --selected: rgba(0, 212, 255, 0.2);
  
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.2);
  --shadow-dark: rgba(0, 0, 0, 0.4);
}

/* ===== RESET ET BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.6;
  transition: var(--transition);
  overflow-x: hidden;
}

/* ===== LAYOUT PRINCIPAL ===== */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: var(--surface);
  border-right: 1px solid var(--border-dark);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: var(--transition);
}

.main-content {
  margin-left: 280px;
  min-height: 100vh;
  background: var(--background-secondary);
  padding: 24px;
  transition: var(--transition);
}

/* ===== HEADER SIDEBAR ===== */
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border);
  background: var(--surface-elevated);
}

.logo {
  text-align: center;
  margin-bottom: 20px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto 12px;
  border: 2px solid var(--primary);
  box-shadow: 0 4px 20px var(--shadow-medium);
}

.logo-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.logo-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 400;
}

/* ===== COMMUTATEUR DE THÈME ===== */
.theme-switcher {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.theme-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.theme-buttons {
  display: flex;
  gap: 4px;
}

.theme-btn {
  width: 36px;
  height: 36px;
  border: 2px solid var(--border);
  border-radius: 50%;
  background: var(--surface);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.theme-btn:hover {
  background: var(--hover);
  border-color: var(--primary);
  transform: scale(1.05);
}

.theme-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--text-inverse);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

/* ===== NAVIGATION ===== */
.nav-menu {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  margin: 2px 12px;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.nav-item:hover {
  background: var(--hover);
  color: var(--text-primary);
  transform: translateX(4px);
}

.nav-item.active {
  background: var(--selected);
  color: var(--primary);
  border-left: 3px solid var(--primary);
}

.nav-icon {
  width: 20px;
  font-size: 18px;
  margin-right: 12px;
  color: var(--primary);
}

.nav-title {
  font-weight: 500;
  font-size: 14px;
}

/* ===== ZONE UTILISATEUR ===== */
.user-info {
  padding: 16px 20px;
  border-top: 1px solid var(--border);
  background: var(--surface-elevated);
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
}

.user-role {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
}

.user-menu-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--surface);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.user-menu-btn:hover {
  background: var(--hover);
  color: var(--primary);
}

/* ===== CONTENU PRINCIPAL ===== */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* ===== BOUTONS ===== */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary);
  color: var(--text-inverse);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-dark);
}

.btn-secondary {
  background: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background: var(--hover);
  border-color: var(--primary);
}

.btn-icon {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--surface);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: var(--hover);
  color: var(--primary);
}

/* ===== CARTES STATISTIQUES ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px var(--shadow-medium);
  border-color: var(--primary);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.success { background: var(--success); }
.stat-icon.info { background: var(--info); }
.stat-icon.warning { background: var(--warning); }
.stat-icon.primary { background: var(--primary); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.stat-change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

/* ===== GRILLE DASHBOARD ===== */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* ===== CARTES ===== */
.card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: 0 2px 8px var(--shadow-light);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 8px 32px var(--shadow-medium);
  border-color: var(--primary);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--surface-variant);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
}

.card-link:hover {
  color: var(--primary-dark);
}

.card-content {
  padding: 24px;
}

/* ===== GRAPHIQUE PLACEHOLDER ===== */
.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--surface-variant);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.chart-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary);
}

.chart-placeholder p {
  font-size: 16px;
  font-weight: 500;
}

/* ===== TABLEAUX ===== */
.table-responsive {
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.modern-table th {
  background: var(--surface-variant);
  color: var(--text-primary);
  font-weight: 600;
  padding: 16px 12px;
  text-align: left;
  border-bottom: 2px solid var(--primary);
}

.modern-table td {
  padding: 16px 12px;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
}

.modern-table tr:hover {
  background: var(--hover);
}

/* ===== BADGES ===== */
.badge {
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

/* ===== ACTIONS RAPIDES ===== */
.quick-actions {
  margin-top: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-btn {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 20px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: var(--text-primary);
}

.action-btn:hover {
  background: var(--hover);
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.action-btn i {
  font-size: 24px;
  color: var(--primary);
}

.action-btn span {
  font-weight: 500;
  font-size: 14px;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .content-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100%;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .theme-switcher {
    flex-direction: column;
    gap: 8px;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* ===== SCROLLBAR PERSONNALISÉE ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-variant);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* ===== FOCUS ET ACCESSIBILITÉ ===== */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
