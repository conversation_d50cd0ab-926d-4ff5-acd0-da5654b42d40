#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Styles spécifiques pour les modules GSCOM
Améliore la visibilité et l'apparence des interfaces
Version moderne avec support multi-thèmes
"""

def get_module_styles(theme_colors=None):
    """Retourne les styles CSS pour les modules avec support des thèmes"""
    if theme_colors is None:
        # Couleurs par défaut (thème sombre)
        theme_colors = {
            'primary': '#00d4ff',
            'secondary': '#6366f1',
            'background': '#1a1a2e',
            'surface': '#2d2d44',
            'text_primary': '#ffffff',
            'text_secondary': 'rgba(255, 255, 255, 0.7)',
            'border': 'rgba(255, 255, 255, 0.2)',
            'hover': 'rgba(0, 212, 255, 0.1)'
        }

    return f"""
        /* Styles pour les onglets des modules */
        QTabWidget::pane {{
            border: 2px solid {theme_colors['primary']}40;
            border-radius: 16px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {theme_colors['surface']}20,
                stop:1 {theme_colors['surface']}10);
            margin-top: 8px;
            padding: 16px;
        }}

        QTabBar::tab {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {theme_colors['surface']}40,
                stop:1 {theme_colors['surface']}20);
            border: 1px solid {theme_colors['border']};
            border-bottom: none;
            border-radius: 12px 12px 0 0;
            padding: 14px 24px;
            margin-right: 4px;
            color: {theme_colors['text_secondary']};
            font-weight: 600;
            font-size: 14px;
            min-width: 120px;
            transition: all 0.3s ease;
        }}
        
        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 212, 255, 0.4),
                stop:1 rgba(0, 212, 255, 0.2));
            border-color: #00d4ff;
            color: #ffffff;
            font-weight: bold;
            border-bottom: 2px solid #00d4ff;
        }
        
        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:1 rgba(255, 255, 255, 0.12));
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        /* Styles pour les GroupBox */
        QGroupBox {
            font-weight: bold;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            margin-top: 15px;
            padding-top: 15px;
            color: #00d4ff;
            background: rgba(255, 255, 255, 0.05);
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #00d4ff;
            font-weight: bold;
            font-size: 14px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 212, 255, 0.2),
                stop:1 rgba(0, 212, 255, 0.1));
            border-radius: 5px;
        }
        
        /* Styles pour les boutons d'action */
        QPushButton[objectName="actionButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.15),
                stop:1 rgba(255, 255, 255, 0.08));
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px 16px;
            color: #ffffff;
            font-weight: 600;
            font-size: 13px;
            min-width: 80px;
        }
        
        QPushButton[objectName="actionButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 212, 255, 0.3),
                stop:1 rgba(0, 212, 255, 0.15));
            border-color: #00d4ff;
            color: #ffffff;
        }
        
        QPushButton[objectName="actionButton"]:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 212, 255, 0.4),
                stop:1 rgba(0, 212, 255, 0.25));
        }
        
        QPushButton[objectName="primaryButton"] {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #0099cc);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            padding: 12px 20px;
            font-size: 14px;
        }
        
        QPushButton[objectName="primaryButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00b8e6, stop:1 #0088bb);
        }
        
        /* Styles pour les champs de saisie */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            background: rgba(255, 255, 255, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 8px;
            padding: 10px 12px;
            color: white;
            font-size: 13px;
            selection-background-color: rgba(0, 212, 255, 0.4);
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border: 2px solid #00d4ff;
            background: rgba(255, 255, 255, 0.18);
        }
        
        QLineEdit::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        /* Styles pour les tableaux */
        QTableWidget {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            gridline-color: rgba(255, 255, 255, 0.15);
            selection-background-color: rgba(0, 212, 255, 0.3);
            alternate-background-color: rgba(255, 255, 255, 0.05);
        }
        
        QTableWidget::item {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }
        
        QTableWidget::item:selected {
            background: rgba(0, 212, 255, 0.4);
            color: #ffffff;
        }
        
        QTableWidget::item:hover {
            background: rgba(0, 212, 255, 0.15);
        }
        
        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 0, 0, 0.5),
                stop:1 rgba(0, 0, 0, 0.3));
            border: none;
            border-bottom: 2px solid #00d4ff;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px;
            font-weight: bold;
            color: #00d4ff;
            font-size: 13px;
        }
        
        /* Styles pour les listes */
        QListWidget {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            selection-background-color: rgba(0, 212, 255, 0.3);
        }
        
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        QListWidget::item:selected {
            background: rgba(0, 212, 255, 0.4);
            color: #ffffff;
        }
        
        QListWidget::item:hover {
            background: rgba(0, 212, 255, 0.15);
        }
        
        /* Styles pour les CheckBox */
        QCheckBox {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        QCheckBox::indicator:checked {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #0099cc);
            border-color: #00d4ff;
        }
        
        QCheckBox::indicator:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.2);
        }
        
        /* Styles pour les labels de titre */
        QLabel[objectName="reportTitle"] {
            font-size: 20px;
            font-weight: bold;
            color: #00d4ff;
            margin: 10px 0;
            padding: 10px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 212, 255, 0.2),
                stop:1 rgba(0, 212, 255, 0.05));
            border-radius: 8px;
            border-left: 4px solid #00d4ff;
        }
        
        /* Styles pour les barres de défilement */
        QScrollBar:vertical {
            background: rgba(255, 255, 255, 0.1);
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }
        
        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 212, 255, 0.6),
                stop:1 rgba(0, 212, 255, 0.4));
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 212, 255, 0.8),
                stop:1 rgba(0, 212, 255, 0.6));
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            height: 0;
            background: transparent;
        }
        
        /* Styles pour les SpinBox */
        QSpinBox, QDoubleSpinBox {
            padding-right: 20px;
        }
        
        QSpinBox::up-button, QDoubleSpinBox::up-button {
            subcontrol-origin: border;
            subcontrol-position: top right;
            width: 20px;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0 8px 0 0;
        }
        
        QSpinBox::down-button, QDoubleSpinBox::down-button {
            subcontrol-origin: border;
            subcontrol-position: bottom right;
            width: 20px;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0 0 8px 0;
        }
        
        QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
        QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
            background: rgba(0, 212, 255, 0.2);
        }
    """

def get_barcode_specific_styles():
    """Styles spécifiques au module codes-barres"""
    return """
        /* Styles pour l'aperçu des codes-barres */
        QLabel[objectName="barcodePreview"] {
            border: 2px dashed rgba(0, 212, 255, 0.4);
            border-radius: 10px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.08),
                stop:1 rgba(255, 255, 255, 0.03));
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }
    """

def get_import_export_specific_styles():
    """Styles spécifiques au module import/export"""
    return """
        /* Styles pour les zones de résultats */
        QTextEdit[objectName="resultsArea"] {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        
        /* Styles pour les listes de templates */
        QListWidget[objectName="templatesList"] {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }
        
        QListWidget[objectName="templatesList"]::item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            margin: 2px;
        }
    """
