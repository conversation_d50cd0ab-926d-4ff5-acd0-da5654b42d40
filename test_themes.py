#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour le nouveau système de thèmes GSCOM
Teste les thèmes sombre, clair et système
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt

# Import du nouveau système de thèmes
from src.ui.styles.theme_manager import theme_manager
from src.ui.components.theme_switcher import ThemeSwitcher, ThemeSettingsDialog
from src.ui.main_window import MainWindow


class ThemeTestWindow(QMainWindow):
    """Fenêtre de test pour les thèmes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des Thèmes GSCOM")
        self.setGeometry(100, 100, 800, 600)
        
        self.init_ui()
        self.connect_theme_manager()
        self.apply_theme()
    
    def init_ui(self):
        """Initialise l'interface de test"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # Titre
        title = QLabel("🎨 Test du Système de Thèmes GSCOM")
        title.setObjectName("testTitle")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Commutateur de thème
        self.theme_switcher = ThemeSwitcher()
        layout.addWidget(self.theme_switcher)
        
        # Informations sur le thème actuel
        self.theme_info = QLabel()
        self.theme_info.setObjectName("themeInfo")
        self.theme_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.theme_info)
        
        # Boutons de test
        test_buttons_layout = QVBoxLayout()
        
        # Bouton primaire
        primary_btn = QPushButton("Bouton Primaire")
        primary_btn.setProperty("class", "primary")
        primary_btn.setObjectName("primaryButton")
        test_buttons_layout.addWidget(primary_btn)
        
        # Bouton secondaire
        secondary_btn = QPushButton("Bouton Secondaire")
        secondary_btn.setProperty("class", "secondary")
        secondary_btn.setObjectName("secondaryButton")
        test_buttons_layout.addWidget(secondary_btn)
        
        # Bouton outline
        outline_btn = QPushButton("Bouton Outline")
        outline_btn.setProperty("class", "outline")
        outline_btn.setObjectName("outlineButton")
        test_buttons_layout.addWidget(outline_btn)
        
        layout.addLayout(test_buttons_layout)
        
        # Bouton pour ouvrir les paramètres avancés
        settings_btn = QPushButton("Paramètres de Thème Avancés")
        settings_btn.clicked.connect(self.show_theme_settings)
        layout.addWidget(settings_btn)
        
        # Bouton pour ouvrir la fenêtre principale
        main_window_btn = QPushButton("Ouvrir Fenêtre Principale GSCOM")
        main_window_btn.clicked.connect(self.show_main_window)
        layout.addWidget(main_window_btn)
        
        layout.addStretch()
        
        self.update_theme_info()
    
    def connect_theme_manager(self):
        """Connecte le gestionnaire de thèmes"""
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def on_theme_changed(self, theme_name: str):
        """Réagit au changement de thème"""
        print(f"🎨 Thème changé vers: {theme_name}")
        self.apply_theme()
        self.update_theme_info()
    
    def apply_theme(self):
        """Applique le thème actuel"""
        colors = theme_manager.get_theme_colors()
        base_styles = theme_manager.get_theme_styles()
        
        # Styles spécifiques à la fenêtre de test
        test_styles = f"""
        QMainWindow {{
            background: {colors.get('background', '#ffffff')};
            color: {colors.get('text_primary', '#1e293b')};
            font-family: 'Segoe UI', sans-serif;
        }}
        
        #testTitle {{
            font-size: 24px;
            font-weight: bold;
            color: {colors.get('primary', '#0066cc')};
            margin: 20px 0;
        }}
        
        #themeInfo {{
            font-size: 16px;
            color: {colors.get('text_secondary', '#64748b')};
            background: {colors.get('surface_variant', '#f1f5f9')};
            padding: 16px;
            border-radius: 8px;
            border: 1px solid {colors.get('border', '#e2e8f0')};
        }}
        
        QPushButton {{
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
            margin: 4px 0;
        }}
        
        QPushButton[class="primary"] {{
            background: {colors.get('primary', '#0066cc')};
            color: {colors.get('text_inverse', '#ffffff')};
            border: none;
        }}
        
        QPushButton[class="primary"]:hover {{
            background: {colors.get('primary_dark', '#004499')};
        }}
        
        QPushButton[class="secondary"] {{
            background: {colors.get('surface', '#ffffff')};
            color: {colors.get('primary', '#0066cc')};
            border: 2px solid {colors.get('primary', '#0066cc')};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background: {colors.get('primary', '#0066cc')};
            color: {colors.get('text_inverse', '#ffffff')};
        }}
        
        QPushButton[class="outline"] {{
            background: transparent;
            color: {colors.get('text_primary', '#1e293b')};
            border: 1px solid {colors.get('border', '#e2e8f0')};
        }}
        
        QPushButton[class="outline"]:hover {{
            background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
            border-color: {colors.get('primary', '#0066cc')};
        }}
        """
        
        complete_styles = base_styles + test_styles
        self.setStyleSheet(complete_styles)
    
    def update_theme_info(self):
        """Met à jour les informations sur le thème"""
        current_theme = theme_manager.current_theme
        theme_obj = theme_manager.get_current_theme()
        is_dark = theme_obj.is_dark() if theme_obj else False
        
        info_text = f"""
        <b>Thème actuel :</b> {current_theme.title()}<br>
        <b>Type :</b> {'Sombre' if is_dark else 'Clair'}<br>
        <b>Couleur primaire :</b> {theme_manager.get_theme_colors().get('primary', 'N/A')}<br>
        <b>Thèmes disponibles :</b> {', '.join(theme_manager.get_available_themes())}
        """
        
        self.theme_info.setText(info_text)
    
    def show_theme_settings(self):
        """Affiche le dialogue des paramètres de thème"""
        dialog = ThemeSettingsDialog(self)
        dialog.exec_()
    
    def show_main_window(self):
        """Ouvre la fenêtre principale GSCOM"""
        try:
            self.main_window = MainWindow()
            self.main_window.show()
            print("✅ Fenêtre principale GSCOM ouverte")
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture de la fenêtre principale: {e}")


def main():
    """Fonction principale de test"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSCOM Theme Test")
    app.setApplicationVersion("1.0")
    
    print("🚀 Démarrage du test des thèmes GSCOM...")
    print(f"📋 Thèmes disponibles: {theme_manager.get_available_themes()}")
    print(f"🎨 Thème actuel: {theme_manager.current_theme}")
    
    # Créer et afficher la fenêtre de test
    test_window = ThemeTestWindow()
    test_window.show()
    
    print("✅ Fenêtre de test affichée")
    print("💡 Utilisez les boutons de thème pour tester les différents modes")
    
    # Lancer l'application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
