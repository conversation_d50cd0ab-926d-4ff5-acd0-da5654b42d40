#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre principale de l'application GSCOM
Interface moderne avec navigation verticale et couleurs sobres professionnelles
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Import du système de navigation unifié
from src.ui.navigation import module_navigator

# Import du système de thèmes moderne
from src.ui.styles.theme_manager import theme_manager
from src.ui.styles.components import get_modern_components_styles
from src.ui.components.theme_switcher import ThemeSwitcher

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application"""

    def __init__(self, user=None):
        super().__init__()
        self.current_user = user
        self.logger = logging.getLogger(__name__)

        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Gestion Commerciale")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # Centrer la fenêtre
        self.center_window()

        # Connecter le gestionnaire de thèmes
        self.connect_theme_manager()

        # Initialiser l'interface
        self.setup_ui()
        self.apply_modern_styles()

        # Configurer le système de navigation (après setup_ui)
        self.setup_navigation()

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def setup_navigation(self):
        """Configure le système de navigation unifié"""
        # Configurer le navigateur avec l'utilisateur et le stack
        module_navigator.set_current_user(self.current_user)
        module_navigator.set_content_stack(self.content_stack)

        # Connecter les signaux
        module_navigator.module_changed.connect(self.on_module_changed)

    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def connect_theme_manager(self):
        """Connecte le gestionnaire de thèmes"""
        theme_manager.theme_changed.connect(self.on_theme_changed)

    def on_theme_changed(self, theme_name: str):
        """Réagit au changement de thème"""
        self.logger.info(f"Changement de thème vers: {theme_name}")
        self.apply_modern_styles()

        # Propager le changement aux widgets enfants
        self.update_child_widgets_theme()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Créer la sidebar
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)

        # Créer la zone de contenu
        self.create_content_area()
        main_layout.addWidget(self.content_area)

    def create_sidebar(self):
        """Crée la barre latérale de navigation"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(280)

        # Layout vertical pour la sidebar
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Header de la sidebar
        self.create_sidebar_header()
        sidebar_layout.addWidget(self.sidebar_header)

        # Zone de navigation avec scroll
        self.create_navigation_area()
        sidebar_layout.addWidget(self.nav_scroll)

        # Zone utilisateur
        self.create_user_area()
        sidebar_layout.addWidget(self.user_info)

    def create_sidebar_header(self):
        """Crée l'en-tête de la sidebar"""
        self.sidebar_header = QFrame()
        self.sidebar_header.setObjectName("sidebarHeader")
        self.sidebar_header.setFixedHeight(160)  # Augmenté pour le commutateur de thème

        header_layout = QVBoxLayout(self.sidebar_header)
        header_layout.setContentsMargins(20, 20, 20, 20)
        header_layout.setSpacing(10)

        # Logo
        logo_label = QLabel("🏢")
        logo_label.setObjectName("logoLabel")
        logo_label.setFixedSize(60, 60)
        logo_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo_label, 0, Qt.AlignCenter)

        # Titre
        title_label = QLabel("GSCOM")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # Sous-titre
        subtitle_label = QLabel("Gestion Commerciale")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)

        # Commutateur de thème
        self.theme_switcher = ThemeSwitcher()
        header_layout.addWidget(self.theme_switcher, 0, Qt.AlignCenter)

    def create_navigation_area(self):
        """Crée la zone de navigation"""
        self.nav_scroll = QScrollArea()
        self.nav_scroll.setObjectName("navScroll")
        self.nav_scroll.setWidgetResizable(True)
        self.nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.nav_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget de navigation
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(0, 10, 0, 10)
        nav_layout.setSpacing(5)

        # Modules de navigation
        modules = [
            ("dashboard", "📊", "Tableau de bord"),
            ("commercial", "💼", "Commercial"),
            ("quotes", "📝", "Devis"),
            ("orders", "📋", "Commandes"),
            ("invoices", "🧾", "Factures"),
            ("clients", "👥", "Clients"),
            ("suppliers", "🏭", "Fournisseurs"),
            ("products", "📦", "Produits"),
            ("stock", "📋", "Stock"),
            ("inventory", "📊", "Inventaire"),
            ("barcode", "🏷️", "Codes-barres"),
            ("accounting", "💰", "Comptabilité"),
            ("reports", "📈", "Rapports"),
            ("import_export", "📥📤", "Import/Export"),
            ("settings", "⚙️", "Paramètres"),
        ]

        self.nav_buttons = {}
        for module_id, icon, title in modules:
            button = self.create_nav_button(module_id, icon, title)
            nav_layout.addWidget(button)
            self.nav_buttons[module_id] = button

        nav_layout.addStretch()
        self.nav_scroll.setWidget(nav_widget)

    def create_nav_button(self, module_id, icon, title):
        """Crée un bouton de navigation"""
        button = QPushButton()
        button.setObjectName("navButton")
        button.setCursor(Qt.PointingHandCursor)
        button.setFixedHeight(60)

        # Layout du bouton
        button_layout = QHBoxLayout(button)
        button_layout.setContentsMargins(15, 10, 15, 10)
        button_layout.setSpacing(15)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        button_layout.addWidget(title_label)

        button_layout.addStretch()

        # Connecter le clic
        button.clicked.connect(lambda: self.load_module_content(module_id))

        return button

    def create_user_area(self):
        """Crée la zone utilisateur"""
        self.user_info = QFrame()
        self.user_info.setObjectName("userInfo")
        self.user_info.setFixedHeight(100)

        user_layout = QVBoxLayout(self.user_info)
        user_layout.setContentsMargins(15, 15, 15, 15)
        user_layout.setSpacing(8)

        # Nom utilisateur
        if self.current_user:
            user_name = self.current_user.full_name
        else:
            user_name = "Utilisateur"

        name_label = QLabel(user_name)
        name_label.setObjectName("userName")
        user_layout.addWidget(name_label)

        # Bouton d'action
        action_button = QPushButton("⚙️ Profil")
        action_button.setObjectName("userActionButton")
        action_button.setFixedHeight(30)
        action_button.clicked.connect(self.show_user_profile)
        user_layout.addWidget(action_button)

    def create_content_area(self):
        """Crée la zone de contenu principal"""
        self.content_area = QFrame()
        self.content_area.setObjectName("contentArea")

        # Layout pour la zone de contenu
        content_layout = QVBoxLayout(self.content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Stack widget pour les différents modules
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)

    def show_dashboard(self):
        """Affiche le tableau de bord moderne"""
        # Utiliser le système de navigation unifié
        module_navigator.navigate_to_module("dashboard")

        # Mettre à jour l'état du bouton de navigation
        self.update_nav_button_state("dashboard")

    def load_module_content(self, module_id):
        """Charge le contenu d'un module via le navigateur unifié"""
        success = module_navigator.navigate_to_module(module_id)
        if success:
            self.update_nav_button_state(module_id)
        return success

    def on_module_changed(self, module_id):
        """Réagit au changement de module"""
        self.logger.info(f"Module actuel: {module_id}")
        self.update_nav_button_state(module_id)

    def update_nav_button_state(self, active_module_id):
        """Met à jour l'état visuel des boutons de navigation"""
        for module_id, button in self.nav_buttons.items():
            if module_id == active_module_id:
                button.setProperty("active", True)
            else:
                button.setProperty("active", False)
            button.style().unpolish(button)
            button.style().polish(button)

    def load_module_content_legacy(self, module_id):
        """Méthode legacy pour la compatibilité - sera supprimée"""
        try:
            if module_id == "clients":
                from src.ui.modules.clients import ClientsWidget

                # Vérifier si le widget existe déjà
                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ClientsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                # Créer le nouveau widget
                clients_widget = ClientsWidget()
                self.content_stack.addWidget(clients_widget)
                self.content_stack.setCurrentWidget(clients_widget)

            elif module_id == "suppliers":
                from src.ui.modules.suppliers import SuppliersWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, SuppliersWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                suppliers_widget = SuppliersWidget()
                self.content_stack.addWidget(suppliers_widget)
                self.content_stack.setCurrentWidget(suppliers_widget)

            elif module_id == "products":
                from src.ui.modules.products import ProductsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ProductsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                products_widget = ProductsWidget()
                self.content_stack.addWidget(products_widget)
                self.content_stack.setCurrentWidget(products_widget)

            elif module_id == "stock":
                from src.ui.modules.stock import StockWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, StockWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                stock_widget = StockWidget()
                self.content_stack.addWidget(stock_widget)
                self.content_stack.setCurrentWidget(stock_widget)

            elif module_id == "commercial":
                from src.ui.modules.commercial import CommercialWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, CommercialWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                commercial_widget = CommercialWidget()
                self.content_stack.addWidget(commercial_widget)
                self.content_stack.setCurrentWidget(commercial_widget)

            elif module_id == "inventory":
                from src.ui.modules.inventory import InventoryWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, InventoryWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                inventory_widget = InventoryWidget()
                self.content_stack.addWidget(inventory_widget)
                self.content_stack.setCurrentWidget(inventory_widget)

            elif module_id == "accounting":
                from src.ui.modules.accounting import AccountingWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, AccountingWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                accounting_widget = AccountingWidget()
                self.content_stack.addWidget(accounting_widget)
                self.content_stack.setCurrentWidget(accounting_widget)

            elif module_id == "reports":
                from src.ui.modules.reports import ReportsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ReportsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                reports_widget = ReportsWidget()
                self.content_stack.addWidget(reports_widget)
                self.content_stack.setCurrentWidget(reports_widget)

            elif module_id == "settings":
                from src.ui.modules.settings import SettingsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, SettingsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                settings_widget = SettingsWidget()
                self.content_stack.addWidget(settings_widget)
                self.content_stack.setCurrentWidget(settings_widget)

            elif module_id == "quotes":
                from src.ui.modules.quotes import QuotesWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, QuotesWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                quotes_widget = QuotesWidget()
                self.content_stack.addWidget(quotes_widget)
                self.content_stack.setCurrentWidget(quotes_widget)

            elif module_id == "orders":
                from src.ui.modules.orders import OrdersWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, OrdersWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                orders_widget = OrdersWidget()
                self.content_stack.addWidget(orders_widget)
                self.content_stack.setCurrentWidget(orders_widget)

            elif module_id == "barcode":
                from src.ui.modules.barcode_manager import BarcodeManagerWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, BarcodeManagerWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                barcode_widget = BarcodeManagerWidget()
                self.content_stack.addWidget(barcode_widget)
                self.content_stack.setCurrentWidget(barcode_widget)

            elif module_id == "import_export":
                from src.ui.modules.import_export import ImportExportWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ImportExportWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                import_export_widget = ImportExportWidget()
                self.content_stack.addWidget(import_export_widget)
                self.content_stack.setCurrentWidget(import_export_widget)

            elif module_id == "invoices":
                from src.ui.modules.invoices import InvoicesWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, InvoicesWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                invoices_widget = InvoicesWidget()
                self.content_stack.addWidget(invoices_widget)
                self.content_stack.setCurrentWidget(invoices_widget)

            elif module_id == "reports":
                from src.ui.modules.reports import ReportsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ReportsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                reports_widget = ReportsWidget()
                self.content_stack.addWidget(reports_widget)
                self.content_stack.setCurrentWidget(reports_widget)

            elif module_id == "accounting":
                from src.ui.modules.accounting import AccountingWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, AccountingWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                accounting_widget = AccountingWidget()
                self.content_stack.addWidget(accounting_widget)
                self.content_stack.setCurrentWidget(accounting_widget)

            elif module_id == "import_export":
                from src.ui.modules.import_export import ImportExportWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ImportExportWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                import_export_widget = ImportExportWidget()
                self.content_stack.addWidget(import_export_widget)
                self.content_stack.setCurrentWidget(import_export_widget)

            elif module_id == "dashboard":
                # Retourner au tableau de bord (index 0)
                self.content_stack.setCurrentIndex(0)

            else:
                # Module non implémenté
                self.show_placeholder_module(module_id)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement du module {module_id}: {e}")
            self.show_error_message(f"Erreur lors du chargement du module: {e}")

    def show_placeholder_module(self, module_id):
        """Affiche un placeholder pour les modules non implémentés"""
        module_titles = {
            "dashboard": "Tableau de bord",
            "commercial": "Gestion Commerciale",
            "quotes": "Gestion des Devis",
            "orders": "Gestion des Commandes",
            "invoices": "Gestion des Factures",
            "clients": "Gestion des Clients",
            "suppliers": "Gestion des Fournisseurs",
            "products": "Gestion des Produits",
            "stock": "Gestion des Stocks",
            "inventory": "Inventaire",
            "barcode": "Gestion des Codes-barres",
            "accounting": "Comptabilité",
            "reports": "Rapports",
            "import_export": "Import / Export",
            "settings": "Paramètres",
        }

        title = module_titles.get(module_id, "Module")

        placeholder = QWidget()
        layout = QVBoxLayout(placeholder)
        layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel("🚧")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 64px;")
        layout.addWidget(icon_label)

        title_label = QLabel(f"{title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ffffff; margin: 20px;")
        layout.addWidget(title_label)

        desc_label = QLabel("Module en cours de développement")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("font-size: 16px; color: rgba(255, 255, 255, 0.7);")
        layout.addWidget(desc_label)

        self.content_stack.addWidget(placeholder)
        self.content_stack.setCurrentWidget(placeholder)

    def show_user_profile(self):
        """Affiche le profil utilisateur"""
        QMessageBox.information(self, "Profil", "Fonctionnalité de profil en cours de développement")

    def show_error_message(self, message):
        """Affiche un message d'erreur"""
        QMessageBox.critical(self, "Erreur", message)

    def apply_modern_styles(self):
        """Applique les styles modernes selon le thème actuel"""
        # Obtenir les couleurs du thème actuel
        colors = theme_manager.get_theme_colors()

        # Styles de base du thème
        base_styles = theme_manager.get_theme_styles()

        # Styles des composants modernes
        component_styles = get_modern_components_styles(colors)

        # Styles spécifiques à la fenêtre principale
        main_window_styles = f"""
        /* === STYLES SPÉCIFIQUES FENÊTRE PRINCIPALE === */
        QMainWindow {{
            background: {colors.get('background', '#ffffff')};
            color: {colors.get('text_primary', '#1e293b')};
            font-family: 'Segoe UI', 'Inter', 'SF Pro Display', system-ui, sans-serif;
        }}

        /* === SIDEBAR === */
        #sidebar {{
            background: {colors.get('gradient_surface', colors.get('surface', '#ffffff'))};
            border-right: 1px solid {colors.get('border_dark', colors.get('border', '#e2e8f0'))};
            border-radius: 0;
        }}

        #sidebarHeader {{
            background: {colors.get('surface_elevated', colors.get('surface', '#ffffff'))};
            border-bottom: 1px solid {colors.get('border', '#e2e8f0')};
            border-radius: 0 0 16px 16px;
            padding: 20px;
        }}

        #logoLabel {{
            background: {colors.get('gradient_primary', colors.get('primary', '#0066cc'))};
            border: 2px solid {colors.get('border_accent', colors.get('primary', '#0066cc'))};
            border-radius: 30px;
            color: {colors.get('text_inverse', '#ffffff')};
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }}

        #titleLabel {{
            color: {colors.get('primary', '#0066cc')};
            font-size: 18px;
            font-weight: 700;
        }}

        #subtitleLabel {{
            color: {colors.get('text_secondary', '#64748b')};
            font-size: 12px;
            font-weight: 400;
        }}

        /* === NAVIGATION === */
        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 14px 16px;
            border-radius: 12px;
            margin: 3px 12px;
            min-height: 48px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}

        #navButton:hover {{
            background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
            border-left: 3px solid {colors.get('primary', '#0066cc')};
            transform: translateX(2px);
        }}

        #navButton:pressed {{
            background: {colors.get('active', 'rgba(0, 102, 204, 0.16)')};
        }}

        #navIcon {{
            color: {colors.get('primary', '#0066cc')};
            font-size: 20px;
            font-weight: bold;
        }}

        #navTitle {{
            color: {colors.get('text_secondary', '#64748b')};
            font-size: 15px;
            font-weight: 600;
        }}

        /* === ZONE UTILISATEUR === */
        #userInfo {{
            background: {colors.get('surface_elevated', colors.get('surface', '#ffffff'))};
            border: 1px solid {colors.get('border', '#e2e8f0')};
            border-radius: 12px;
            margin: 12px;
        }}

        #userName {{
            color: {colors.get('primary', '#0066cc')};
            font-size: 14px;
            font-weight: bold;
        }}

        #userActionButton {{
            background: {colors.get('surface', '#ffffff')};
            border: 1px solid {colors.get('border', '#e2e8f0')};
            border-radius: 8px;
            color: {colors.get('text_primary', '#1e293b')};
            font-size: 14px;
            font-weight: 500;
        }}

        #userActionButton:hover {{
            background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
            border-color: {colors.get('primary', '#0066cc')};
        }}

        /* === ZONE DE CONTENU === */
        #contentArea {{
            background: {colors.get('background_secondary', colors.get('background', '#ffffff'))};
            border-radius: 16px 0 0 0;
        }}

        /* === SCROLLBARS === */
        #navScroll {{
            background: transparent;
            border: none;
        }}

        #navScroll QScrollBar:vertical {{
            background: {colors.get('surface_variant', '#f1f5f9')};
            width: 8px;
            border-radius: 4px;
            margin: 0;
        }}

        #navScroll QScrollBar::handle:vertical {{
            background: {colors.get('primary', '#0066cc')};
            border-radius: 4px;
            min-height: 20px;
        }}

        #navScroll QScrollBar::handle:vertical:hover {{
            background: {colors.get('primary_dark', '#004499')};
        }}

        #navScroll QScrollBar::add-line:vertical,
        #navScroll QScrollBar::sub-line:vertical {{
            height: 0;
            background: transparent;
        }}
        """

        # Combiner tous les styles
        complete_styles = base_styles + component_styles + main_window_styles

        # Appliquer les styles
        self.setStyleSheet(complete_styles)

    def update_child_widgets_theme(self):
        """Met à jour le thème des widgets enfants"""
        # Mettre à jour le commutateur de thème
        if hasattr(self, 'theme_switcher'):
            self.theme_switcher.apply_styles()

        # Mettre à jour les widgets dans le stack
        for i in range(self.content_stack.count()):
            widget = self.content_stack.widget(i)
            if hasattr(widget, 'apply_theme'):
                widget.apply_theme()
