#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Composant de commutateur de thème moderne pour GSCOM
Interface élégante pour changer entre thèmes sombre, clair et système
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from src.ui.styles.theme_manager import theme_manager


class ThemeSwitcher(QWidget):
    """Commutateur de thème moderne avec animations"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_theme = theme_manager.current_theme
        self.init_ui()
        self.setup_animations()
        self.connect_signals()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Label du thème
        self.theme_label = QLabel("Thème:")
        self.theme_label.setObjectName("themeLabel")
        layout.addWidget(self.theme_label)
        
        # Boutons de thème
        self.theme_buttons = {}
        themes = [
            ("dark", "🌙", "Sombre"),
            ("light", "☀️", "Clair"),
            ("system", "🖥️", "Système")
        ]
        
        self.button_group = QButtonGroup(self)
        
        for theme_id, icon, tooltip in themes:
            button = QPushButton(icon)
            button.setObjectName("themeButton")
            button.setToolTip(f"Thème {tooltip}")
            button.setCheckable(True)
            button.setFixedSize(40, 40)
            button.clicked.connect(lambda checked, t=theme_id: self.change_theme(t))
            
            self.theme_buttons[theme_id] = button
            self.button_group.addButton(button)
            layout.addWidget(button)
        
        # Sélectionner le thème actuel
        if self.current_theme in self.theme_buttons:
            self.theme_buttons[self.current_theme].setChecked(True)
        
        self.apply_styles()
    
    def setup_animations(self):
        """Configure les animations"""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def connect_signals(self):
        """Connecte les signaux"""
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def change_theme(self, theme_name: str):
        """Change le thème de l'application"""
        if theme_name != self.current_theme:
            self.current_theme = theme_name
            theme_manager.set_theme(theme_name)
            
            # Animation de feedback
            self.animate_change()
    
    def on_theme_changed(self, theme_name: str):
        """Réagit au changement de thème"""
        self.current_theme = theme_name
        
        # Mettre à jour la sélection
        if theme_name in self.theme_buttons:
            self.theme_buttons[theme_name].setChecked(True)
        
        # Réappliquer les styles
        self.apply_styles()
    
    def animate_change(self):
        """Animation lors du changement de thème"""
        # Animation de pulsation
        for button in self.theme_buttons.values():
            if button.isChecked():
                effect = QGraphicsOpacityEffect()
                button.setGraphicsEffect(effect)
                
                self.pulse_animation = QPropertyAnimation(effect, b"opacity")
                self.pulse_animation.setDuration(200)
                self.pulse_animation.setStartValue(1.0)
                self.pulse_animation.setEndValue(0.5)
                self.pulse_animation.finished.connect(lambda: self.pulse_back(effect))
                self.pulse_animation.start()
                break
    
    def pulse_back(self, effect):
        """Animation de retour"""
        self.pulse_back_animation = QPropertyAnimation(effect, b"opacity")
        self.pulse_back_animation.setDuration(200)
        self.pulse_back_animation.setStartValue(0.5)
        self.pulse_back_animation.setEndValue(1.0)
        self.pulse_back_animation.start()
    
    def apply_styles(self):
        """Applique les styles selon le thème actuel"""
        colors = theme_manager.get_theme_colors()
        
        style = f"""
        #themeLabel {{
            color: {colors.get('text_secondary', '#64748b')};
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
        }}
        
        #themeButton {{
            background: {colors.get('surface', '#ffffff')};
            border: 2px solid {colors.get('border', '#e2e8f0')};
            border-radius: 20px;
            font-size: 16px;
            color: {colors.get('text_primary', '#1e293b')};
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        #themeButton:hover {{
            background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
            border-color: {colors.get('primary', '#0066cc')};
            transform: scale(1.05);
        }}
        
        #themeButton:checked {{
            background: {colors.get('primary', '#0066cc')};
            border-color: {colors.get('primary', '#0066cc')};
            color: {colors.get('text_inverse', '#ffffff')};
        }}
        
        #themeButton:pressed {{
            transform: scale(0.95);
        }}
        """
        
        self.setStyleSheet(style)


class ThemeSettingsDialog(QDialog):
    """Dialogue de paramètres de thème avancés"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Paramètres de thème")
        self.setFixedSize(400, 300)
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface du dialogue"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Titre
        title = QLabel("Personnalisation du thème")
        title.setObjectName("dialogTitle")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sélecteur de thème
        theme_group = QGroupBox("Thème de l'interface")
        theme_layout = QVBoxLayout(theme_group)
        
        self.theme_switcher = ThemeSwitcher()
        theme_layout.addWidget(self.theme_switcher)
        
        layout.addWidget(theme_group)
        
        # Options avancées
        advanced_group = QGroupBox("Options avancées")
        advanced_layout = QFormLayout(advanced_group)
        
        self.auto_switch_checkbox = QCheckBox("Changer automatiquement selon l'heure")
        advanced_layout.addRow(self.auto_switch_checkbox)
        
        self.accent_color_button = QPushButton("Couleur d'accent personnalisée")
        self.accent_color_button.clicked.connect(self.choose_accent_color)
        advanced_layout.addRow("Couleur:", self.accent_color_button)
        
        layout.addWidget(advanced_group)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.apply_button = QPushButton("Appliquer")
        self.apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(self.apply_button)
        
        self.close_button = QPushButton("Fermer")
        self.close_button.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_button)
        
        layout.addLayout(buttons_layout)
        
        self.apply_styles()
    
    def choose_accent_color(self):
        """Ouvre le sélecteur de couleur d'accent"""
        color = QColorDialog.getColor(QColor("#0066cc"), self, "Choisir une couleur d'accent")
        if color.isValid():
            self.accent_color_button.setStyleSheet(f"background-color: {color.name()};")
    
    def apply_settings(self):
        """Applique les paramètres"""
        # Ici on pourrait sauvegarder les paramètres avancés
        self.accept()
    
    def apply_styles(self):
        """Applique les styles au dialogue"""
        colors = theme_manager.get_theme_colors()
        
        style = f"""
        QDialog {{
            background: {colors.get('background', '#ffffff')};
            color: {colors.get('text_primary', '#1e293b')};
        }}
        
        #dialogTitle {{
            font-size: 18px;
            font-weight: bold;
            color: {colors.get('primary', '#0066cc')};
            margin-bottom: 10px;
        }}
        
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {colors.get('border', '#e2e8f0')};
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {colors.get('primary', '#0066cc')};
        }}
        
        QPushButton {{
            background: {colors.get('surface', '#ffffff')};
            border: 1px solid {colors.get('border', '#e2e8f0')};
            border-radius: 6px;
            padding: 8px 16px;
            color: {colors.get('text_primary', '#1e293b')};
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background: {colors.get('hover', 'rgba(0, 102, 204, 0.08)')};
            border-color: {colors.get('primary', '#0066cc')};
        }}
        """
        
        self.setStyleSheet(style)
